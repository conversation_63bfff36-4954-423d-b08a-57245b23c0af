import 'package:flutter/material.dart';
import 'package:lifeitude/constraints/app_colours.dart';

class TimePickerScroll extends StatefulWidget {
  final int caloriesBurned;
  final Function(int hours, int minutes, int totalCalories) onTimeChanged;
  const TimePickerScroll({
    super.key,
    required this.onTimeChanged,
    required this.caloriesBurned,
  });

  @override
  State<TimePickerScroll> createState() => _TimePickerScrollState();
}

class _TimePickerScrollState extends State<TimePickerScroll> {
  late FixedExtentScrollController _hoursController;
  late FixedExtentScrollController _minutesController;

  int _selectedHours = 0;
  int _selectedMinutes = 0;

  @override
  void initState() {
    super.initState();
    _hoursController = FixedExtentScrollController(initialItem: 0);
    _minutesController = FixedExtentScrollController(initialItem: 0);
  }

  @override
  void dispose() {
    _hoursController.dispose();
    _minutesController.dispose();
    super.dispose();
  }

  int _calculateTotalCalories() {
    // Calculate total calories based on hours, minutes, and initial caloriesBurned
    int totalTime = (_selectedHours * 60) + _selectedMinutes;
    int totalCalories = widget.caloriesBurned * totalTime;
    return totalCalories;
  }

  void _adjustTimeIfNeeded() {
    if (_selectedHours == 0 && _selectedMinutes == 0) {
      // If both hours and minutes are zero, scroll minutes to 01
      _minutesController.animateToItem(1,
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
      setState(() {
        _selectedMinutes = 1;
      });
      widget.onTimeChanged(
          _selectedHours, _selectedMinutes, _calculateTotalCalories());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Hours Column
            Column(
              children: [
                SizedBox(
                  width: 100,
                  height: 200,
                  child: ListWheelScrollView.useDelegate(
                    controller: _hoursController,
                    itemExtent: 50,
                    perspective: 0.005,
                    diameterRatio: 1.2,
                    physics: const FixedExtentScrollPhysics(),
                    childDelegate: ListWheelChildBuilderDelegate(
                      childCount: 24, // 0-23 hours
                      builder: (context, index) {
                        return _buildNumberContainer(index.toString(),
                            isSelected: index == _selectedHours);
                      },
                    ),
                    onSelectedItemChanged: (index) {
                      setState(() {
                        _selectedHours = index;
                      });
                      _adjustTimeIfNeeded();
                      widget.onTimeChanged(_selectedHours, _selectedMinutes,
                          _calculateTotalCalories());
                    },
                  ),
                ),
                const Text(
                  'Hours',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColours.appOrgange,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 20),

            // Minutes Column
            Column(
              children: [
                SizedBox(
                  width: 100,
                  height: 200,
                  child: ListWheelScrollView.useDelegate(
                    controller: _minutesController,
                    itemExtent: 50,
                    perspective: 0.005,
                    diameterRatio: 1.2,
                    physics: const FixedExtentScrollPhysics(),
                    childDelegate: ListWheelChildBuilderDelegate(
                      childCount: 60, // 0-59 minutes
                      builder: (context, index) {
                        return _buildNumberContainer(
                            index.toString().padLeft(2, '0'),
                            isSelected: index == _selectedMinutes);
                      },
                    ),
                    onSelectedItemChanged: (index) {
                      setState(() {
                        _selectedMinutes = index;
                      });
                      _adjustTimeIfNeeded();
                      widget.onTimeChanged(_selectedHours, _selectedMinutes,
                          _calculateTotalCalories());
                    },
                  ),
                ),
                const Text(
                  'Minutes',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColours.appOrgange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  // Custom widget to build number containers
  Widget _buildNumberContainer(String number, {bool isSelected = false}) {
    return Container(
      width: 80,
      height: 50,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: isSelected ? AppColours.appOrgange : Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        number,
        style: TextStyle(
          fontSize: 24,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? Colors.white : Colors.black,
        ),
      ),
    );
  }
}
