import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';
import '../../../../constraints/app_colours.dart';
import '../../../../constraints/app_padding_only.dart';
import '../../../../notifier_controllers/recipes_controller.dart';

class RecipesScreen extends StatelessWidget with WatchItMixin {
  const RecipesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final recipes = watchIt<RecipesController>();

    return Scaffold(
      backgroundColor: AppColours.appLiteOrange,
      body: Padding(
        padding: const EdgeInsets.only(
          left: AppPaddingOnly.leftRight,
          right: AppPaddingOnly.leftRight,
          top: AppPaddingOnly.top,
        ),
        child: ListView(
          scrollDirection: Axis.horizontal,
          children: [Text("jkjdks")],
        ),
      ),
    );
  }
}
