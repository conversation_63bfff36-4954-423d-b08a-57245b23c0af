import 'package:flutter/material.dart';
import 'package:lifeitude/notifier_controllers/recipes_controller.dart';
import 'package:watch_it/watch_it.dart';

import '../../../constraints/app_colours.dart';
import '../../../widgets/my_appbar.dart';

class RecipeSearchScreen extends WatchingStatefulWidget {
  final String recipeType;

  const RecipeSearchScreen({super.key, required this.recipeType});

  @override
  State<RecipeSearchScreen> createState() => _RecipeSearchScreenState();
}

class _RecipeSearchScreenState extends State<RecipeSearchScreen> {
  @override
  void initState() {
    super.initState();

    // Schedule the API call to run after the current build frame completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      di<RecipesController>().getRecipesByType(widget.recipeType);
    });
  }

  @override
  Widget build(BuildContext context) {
    final recipesController = watchIt<RecipesController>();

    return Scaffold(
      backgroundColor: AppColours.appLiteOrange,
      appBar: MyAppBar(title: widget.recipeType, canPop: true),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColours.appOrgange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColours.appOrgange.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    widget.recipeType,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: recipesController.isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : recipesController.recipesModel?.recipes?.recipe?.isEmpty ??
                        true
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.restaurant_menu,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No recipes found',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Try searching for a different recipe type',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount:
                          recipesController
                              .recipesModel
                              ?.recipes
                              ?.recipe
                              ?.length ??
                          0,
                      itemBuilder: (context, index) {
                        final recipe = recipesController
                            .recipesModel!
                            .recipes!
                            .recipe![index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 16),
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  recipe.recipeName ?? 'Unknown Recipe',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                if (recipe.recipeDescription != null)
                                  Text(
                                    recipe.recipeDescription!,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey,
                                    ),
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                const SizedBox(height: 12),
                                if (recipe.recipeNutrition != null)
                                  Row(
                                    children: [
                                      if (recipe.recipeNutrition!.calories !=
                                          null)
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: AppColours.appOrgange
                                                .withValues(alpha: 0.2),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          child: Text(
                                            '${recipe.recipeNutrition!.calories} cal',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: AppColours.appGreen,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getRecipeIcon(String recipeType) {
    switch (recipeType.toLowerCase()) {
      case 'appetizer':
        return Icons.restaurant_menu;
      case 'soup':
        return Icons.soup_kitchen;
      case 'main dish':
        return Icons.dinner_dining;
      case 'side dish':
        return Icons.rice_bowl;
      case 'baked':
        return Icons.bakery_dining;
      case 'salad and salad dressing':
        return Icons.eco;
      case 'sauce and condiment':
        return Icons.water_drop;
      case 'dessert':
        return Icons.cake;
      case 'snack':
        return Icons.cookie;
      case 'beverage':
        return Icons.local_drink;
      case 'breakfast':
        return Icons.free_breakfast;
      case 'lunch':
        return Icons.lunch_dining;
      case 'other':
        return Icons.more_horiz;
      default:
        return Icons.restaurant;
    }
  }
}
