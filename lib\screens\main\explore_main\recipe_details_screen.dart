import 'package:flutter/material.dart';
import 'package:lifeitude/widgets/my_appbar.dart';
import 'package:watch_it/watch_it.dart';

import '../../../notifier_controllers/recipes_controller.dart';

class RecipeDetailsScreen extends StatelessWidget with WatchItMixin {
  final String recipeId;

  const RecipeDetailsScreen({super.key, required this.recipeId});

  @override
  Widget build(BuildContext context) {

    final recipesDetails = watchIt<RecipesController>();
    return Scaffold(
      appBar: MyAppBar(title: 'Recipe Details', canPop: true),
      body: const Center(child: Text('Recipe Details')),
    );
  }
}
