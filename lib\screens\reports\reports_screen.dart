import 'package:flutter/material.dart';
import 'package:lifeitude/widgets/my_appbar.dart';

import '../../constraints/app_colours.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: MyAppBar(
        dontShowProfileIcon: true,
        title: 'Reports',
        canPop: true,
      ),
      body: const Center(child: Text('Reports')),
    );
  }
}
