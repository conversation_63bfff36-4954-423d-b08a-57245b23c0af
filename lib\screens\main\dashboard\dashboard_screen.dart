import 'package:flutter/material.dart';

import '../../../components/activity_ring_widget.dart';
import '../../../components/circle_rating_widget.dart';
import '../../../components/macronutrient_pie_chart.dart';
import '../../../components/weekly_bar_chart.dart';
import '../../../constraints/app_colours.dart';
import '../../../constraints/app_padding_only.dart';
import '../../../constraints/app_text_style.dart';
import '../../../constraints/design_stuff.dart';
import '../../../widgets/my_appbar.dart';

class Dashboard extends StatelessWidget {
  const Dashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: const MyAppBar(title: 'Dashboard', canPop: false),
      body: Padding(
        padding: const EdgeInsets.only(
            left: AppPaddingOnly.leftRight,
            right: AppPaddingOnly.leftRight,
            top: AppPaddingOnly.top),
        child: ListView(
          children: [
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 2),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Calories',
                          style: AppTextStyle.cardHeaderText,
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(
                            Icons.open_in_new,
                            size: 30.0,
                          ),
                          color: AppColours.appOrgange,
                        ),
                      ],
                    ),
                    const Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: ActivityRingWidget(
                            height: 90,
                            currentValue: 250,
                            maxValue: 2500,
                          ),
                        ),
                        Spacer(),
                        WeeklyBarChart(
                          chartHeight: 110,
                          weeklyValues: [500, 1200, 800, 1800, 300, 2000, 1500],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: 2,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 2),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Macronutrient',
                          style: AppTextStyle.cardHeaderText,
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(
                            Icons.open_in_new,
                            size: 30.0,
                          ),
                          color: AppColours.appOrgange,
                        ),
                      ],
                    ),
                    const MacronutrientPieChart(
                      fat: 30,
                      carbs: 40,
                      protein: 30,
                      height: 180,
                      width: double.infinity,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 2),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Achievements',
                          style: AppTextStyle.cardHeaderText,
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(
                            Icons.open_in_new,
                            size: 30.0,
                          ),
                          color: AppColours.appOrgange,
                        ),
                      ],
                    ),
                    const CircleRatingWidget(
                      isNetworkImage: false,
                      images: [
                        'assets/images/temp/achievement.png',
                        'assets/images/temp/achievement.png',
                        'assets/images/temp/achievement.png',
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
