import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../local_db/models/weight_log_model.dart';
import '../repositories/i_weight_add_local_db.dart';

class WeightLogController extends ChangeNotifier {
  WeightLogController() {
    init();
  }

  List<WeightLogModel> _weightLogs = [];
  List<WeightLogModel> get weightLogs => _weightLogs;

  double _todaysWeight = 0;
  double get todaysWeight => _todaysWeight;

  Future<void> init() async {
    debugPrint('WeightLogController initialized');

    await getWeightLogs();
  }

  Future<void> getWeightLogs() async {
    _weightLogs = await di<IWeightAddLocalDb>().getWeightLogs();
    notifyListeners();
  }

  Future<void> setWeight(double parse) async {
    debugPrint('Weight set to: $parse');

    try {
      final newWeightLog = WeightLogModel(
        id: 0,
        weight: parse,
        date: DateTime.now(),
      );

      await di<IWeightAddLocalDb>().addWeightLog(newWeightLog);

      debugPrint('Weight log added to database');
    } catch (e) {
      debugPrint('Error adding weight log: $e');
    }

    notifyListeners();
    await getWeightLogs();
  }

  void getTodaysWeight() async {
    final toDayweightLogs = await di<IWeightAddLocalDb>().getTodaysWeightLogs();

    _todaysWeight = toDayweightLogs.fold(
      0.0,
      (sum, weightLog) => sum + weightLog.weight,
    );

    notifyListeners();
  }
}
