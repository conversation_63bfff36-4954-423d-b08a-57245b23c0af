import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifeitude/constraints/app_text_style.dart';
import 'package:lifeitude/constraints/design_stuff.dart';
import 'package:watch_it/watch_it.dart';

import '../../../../constraints/app_colours.dart';
import '../../../../constraints/app_padding_only.dart';
import '../../../explore/notifiers/add_friend_notifier.dart';

class FriendsScreen extends StatelessWidget with WatchItMixin {
  const FriendsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final friendsList = watchIt<AddFriendNotifier>();
    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      body: Padding(
        padding: const EdgeInsets.only(
            left: AppPaddingOnly.leftRight,
            right: AppPaddingOnly.leftRight,
            top: AppPaddingOnly.top),
        child: Column(
          children: [
            const SizedBox(height: 10.0),
            GestureDetector(
              onTap: () {
                context.push('/addFriendScreen');
              },
              child: const Card(
                elevation: DesignStuff.cardElevation,
                child: ListTile(
                  leading: Icon(
                    Icons.add_circle_outline_rounded,
                    size: 40.0,
                  ),
                  title: Text(
                    'Add a friend to your group',
                    style: AppTextStyle.bodyText,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10.0),
            ListView.builder(
              shrinkWrap: true,
              itemCount: friendsList.friends.length,
              itemBuilder: (context, index) {
                return Card(
                  elevation: DesignStuff.cardElevation,
                  child: ListTile(
                    leading: const CircleAvatar(
                      backgroundImage: CachedNetworkImageProvider(
                        "https://www.gravatar.com/avatar/2c7d99fe281ecd3bcd65ab915bac6dd5?s=250",
                      ),
                    ),
                    title: Text(
                      friendsList.friends[index].name,
                      style: AppTextStyle.bodyText,
                    ),
                    subtitle: Text(
                      friendsList.friends[index].email,
                      style: AppTextStyle.bodyText,
                    ),
                    trailing: IconButton(
                      icon: const Icon(
                        size: 30.0,
                        Icons.open_in_new,
                        color: AppColours.appOrgange,
                      ),
                      onPressed: () {},
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
