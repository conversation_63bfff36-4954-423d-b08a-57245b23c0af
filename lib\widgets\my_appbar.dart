import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../constraints/app_icons_sizes.dart';

class MyAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool canPop;
  final Color? backgroundColor;
  final bool? dontShowProfileIcon;
  const MyAppBar({
    super.key,
    required this.title,
    required this.canPop,
    this.backgroundColor,
    this.dontShowProfileIcon,
  });

  @override
  Size get preferredSize => const Size.fromHeight(60);

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60.0),
      child: AppBar(
        leading: canPop
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () {
                  context.pop();
                },
              )
            : const SizedBox.shrink(),
        actions: [
          if (dontShowProfileIcon == true)
            const SizedBox.shrink()
          else
            IconButton(
              onPressed: () {
                context.push('/profileSettingScreen');
              },
              icon: const Icon(
                Icons.account_circle,
                size: AppIconsSizes.profileIcon,
                color: Colors.white,
              ),
            ),
        ],
        centerTitle: true,
        title: Text(title),
      ),
    );
  }
}
