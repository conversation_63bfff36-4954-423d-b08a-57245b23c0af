import '../local_db/models/exercies_list.dart';

class ExerciseCistTempData {
  static final List<ExerciseList> exerciseList = [
    ExerciseList(id: 1, name: 'Running', caloriesBurned: 10),
    ExerciseList(id: 2, name: 'Crunch<PERSON>', caloriesBurned: 8),
    ExerciseList(id: 3, name: 'Aerobics', caloriesBurned: 11),
    ExerciseList(id: 4, name: 'Jumping Jacks', caloriesBurned: 7),
    ExerciseList(id: 5, name: 'Squats', caloriesBurned: 5),
    ExerciseList(id: 6, name: '<PERSON>ush<PERSON>', caloriesBurned: 8),
    ExerciseList(id: 7, name: 'Situps', caloriesBurned: 6),
    ExerciseList(id: 8, name: 'Plank', caloriesBurned: 9),
    ExerciseList(id: 9, name: '<PERSON>', caloriesBurned: 4),
    ExerciseList(id: 10, name: '<PERSON><PERSON>', caloriesBurned: 6),
    ExerciseList(id: 11, name: 'Archer<PERSON>', caloriesBurned: 9),
    ExerciseList(id: 12, name: 'Arm Lift', caloriesBurned: 5),
    ExerciseList(id: 13, name: 'Badminton', caloriesBurned: 13),
    ExerciseList(id: 14, name: 'Baseball', caloriesBurned: 11),
    ExerciseList(id: 15, name: 'Cycling', caloriesBurned: 9),
    ExerciseList(id: 16, name: 'Dancing', caloriesBurned: 20),
    ExerciseList(id: 17, name: 'Boxing', caloriesBurned: 34),
    ExerciseList(id: 18, name: 'Football', caloriesBurned: 20),
    ExerciseList(id: 19, name: 'Swimming', caloriesBurned: 27),
    ExerciseList(id: 20, name: 'Rowing Machine', caloriesBurned: 13),
    ExerciseList(id: 21, name: 'Walking', caloriesBurned: 7),
    ExerciseList(id: 22, name: 'Elliptical Machine', caloriesBurned: 20),
    ExerciseList(id: 23, name: 'Weightlifting', caloriesBurned: 7),
    ExerciseList(id: 24, name: 'Bodyweight', caloriesBurned: 8),
    ExerciseList(id: 25, name: 'Zumba', caloriesBurned: 16),
    ExerciseList(id: 26, name: 'Tennis', caloriesBurned: 20),
  ];
}
