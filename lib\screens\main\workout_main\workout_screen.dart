import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../constraints/app_colours.dart';
import '../../../constraints/app_padding_only.dart';
import '../../../constraints/design_stuff.dart';
import '../../../widgets/my_appbar.dart';

class WorkoutScreen extends StatelessWidget {
  const WorkoutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: const MyAppBar(title: 'Workout', canPop: false),
      body: Padding(
        padding: const EdgeInsets.only(
            left: AppPaddingOnly.leftRight,
            right: AppPaddingOnly.leftRight,
            top: AppPaddingOnly.top),
        child: ListView(
          children: [
            InkWell(
              onTap: () {
                context.push('/scheduleVrWorkout');
              },
              child: Card(
                semanticContainer: true,
                clipBehavior: Clip.antiAliasWithSaveLayer,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
                elevation: DesignStuff.cardElevation,
                //margin: const EdgeInsets.all(10),
                child: Image.asset(
                  fit: BoxFit.fill,
                  'assets/images/quest3tab.png',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
