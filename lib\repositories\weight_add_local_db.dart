import '../local_db/models/weight_log_model.dart';
import '../objectbox.g.dart';
import 'i_weight_add_local_db.dart';

class WeightAddLocalDb implements IWeightAddLocalDb {
  final Box<WeightLogModel> _box;

  WeightAddLocalDb(this._box);

  @override
  Future<void> addWeightLog(WeightLogModel weightLog) async {
    _box.put(weightLog);
  }

  @override
  Future<List<WeightLogModel>> getWeightLogs() async {
    return _box.getAll();
  }

  @override
  Future<void> deleteWeightLog(WeightLogModel weightLog) async {
    _box.remove(weightLog.id);
  }
}
