import 'package:flutter/material.dart';
import 'package:lifeitude/constraints/app_colours.dart';

class CircleRatingWidget extends StatefulWidget {
  final List<String> images;
  final double? height;
  final bool isNetworkImage;
  const CircleRatingWidget({
    super.key,
    required this.images,
    this.height,
    required this.isNetworkImage,
  });

  @override
  State<CircleRatingWidget> createState() => _CircleRatingWidgetState();
}

class _CircleRatingWidgetState extends State<CircleRatingWidget> {
  late List<List<int>> _starStates;

  @override
  void initState() {
    super.initState();
    // Initialize star states (0: grey, 1: yellow, 2: orange)
    _starStates = List.generate(3, (_) => List.generate(3, (_) => 0));

    _starStates[0][0] = 2;
    _starStates[0][1] = 2;
  }

  void _cycleStar(int rowIndex, int starIndex) {
    setState(() {
      // Cycle through states: 0 (grey) -> 1 (yellow) -> 2 (orange) -> back to 0
      _starStates[rowIndex][starIndex] =
          (_starStates[rowIndex][starIndex] + 1) % 3;
    });
  }

  Color _getStarColor(int state) {
    switch (state) {
      case 1:
        return Colors.yellow;
      case 2:
        return Colors.orange;
      default:
        return Colors.grey.shade400;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Circles Row
        SizedBox(
          height: widget.height ?? 130,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              3,
              (index) {
                return Column(
                  children: [
                    Container(
                      width: widget.height != null ? widget.height! : 100,
                      height: widget.height != null ? widget.height! : 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColours.appGreenDark,
                          width: 3,
                        ),
                      ),
                      child: widget.isNetworkImage
                          ? Center(
                              child: ClipOval(
                                child: Image.network(
                                  widget.images[index],
                                  width: widget.height != null
                                      ? widget.height! * 0.8
                                      : 80,
                                  height: widget.height != null
                                      ? widget.height! * 0.8
                                      : 80,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Icon(
                                      Icons.error,
                                      size: widget.height != null
                                          ? widget.height! * 0.5
                                          : 50,
                                    );
                                  },
                                ),
                              ),
                            )
                          : Center(
                              child: ClipOval(
                                child: Image.asset(
                                  widget.images[index],
                                  width: widget.height != null
                                      ? widget.height! * 0.8
                                      : 80,
                                  height: widget.height != null
                                      ? widget.height! * 0.8
                                      : 80,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Icon(
                                      Icons.error,
                                      size: widget.height != null
                                          ? widget.height! * 0.5
                                          : 50,
                                    );
                                  },
                                ),
                              ),
                            ),
                    ),

                    // Stars Row for each image
                    Row(
                      children: List.generate(3, (starIndex) {
                        return GestureDetector(
                          onTap: () => _cycleStar(index, starIndex),
                          child: Icon(
                            Icons.star,
                            color: _getStarColor(_starStates[index][starIndex]),
                            size: 30,
                          ),
                        );
                      }),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
