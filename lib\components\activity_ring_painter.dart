import 'dart:math';

import 'package:flutter/material.dart';
import 'package:lifeitude/constraints/app_colours.dart';

class ActivityRingPainter extends CustomPainter {
  final double progress;

  ActivityRingPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;
    const ringWidth = 20.0;

    // Background (grey) ring
    final backgroundPaint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = ringWidth
      ..strokeCap = StrokeCap.round;

    // Foreground (green) ring
    final foregroundPaint = Paint()
      ..color = AppColours.appGreen
      ..style = PaintingStyle.stroke
      ..strokeWidth = ringWidth
      ..strokeCap = StrokeCap.round;

    // Draw background ring
    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw foreground (progress) ring
    final sweepAngle = 2 * pi * progress;
    final rect = Rect.fromCircle(center: center, radius: radius);

    canvas.drawArc(
        rect,
        -pi / 2, // Start from the top
        sweepAngle,
        false,
        foregroundPaint);
  }

  @override
  bool shouldRepaint(covariant ActivityRingPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
