import 'dart:math';

import 'package:flutter/material.dart';

import '../../../local_db/models/friends_list.dart';
import '../../../local_db/models/vr_friends_list.dart';

class AddFriendNotifier extends ChangeNotifier {
  String? _name;
  String? _email;

  String? get name => _name;
  String? get email => _email;

  final List<FriendsList> _friends = [];

  List<FriendsList> get friends => _friends;

  final List<VrFriendsList> _vrFriends = [];

  List<VrFriendsList> get vrFriends => _vrFriends;

  AddFriendNotifier() {
    init();
  }

  void init() {
    // Add your initialization code here
    debugPrint('AddFriendNotifier initialized');

    //for test add some friends
    _friends.add(
      FriendsList(
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
      ),
    );
    _friends.add(
      FriendsList(
        id: '2',
        name: '<PERSON>',
        email: '<PERSON>@dean.com',
      ),
    );
    //for test add some friends
  }

  void setNameAndEmail(String name, String email) {
    _name = name;
    _email = email;

    String id = Random().nextInt(1000).toString();

    _friends.add(FriendsList(id: id, name: name, email: email));
    debugPrint('Name: $name, Email: $email');
    notifyListeners();
  }

  void addFriendToVR(
      String id, String name, String email, DateTime dates) {
    debugPrint(' id: $id Name: $name, Email: $email, Dates: $dates');

    String bookID = Random().nextInt(1000).toString();

    //TODO: Add to VR friends list

    //we need to have one booking with the same id, muiltable person can book on same date

    _vrFriends.add(
      VrFriendsList(
        bookByID: 'myid',
        name: name,
        email: email,
        userBookedID: id,
        date: dates,
        bookID: bookID,
      ),
    );

    notifyListeners();
  }

  void removeVrFriend(int index) {
    vrFriends.removeAt(index);
    notifyListeners();
  }
}
