import '../local_db/models/water_intake.dart';
import '../objectbox.g.dart';
import 'i_add_water_local_db.dart';

class AddWaterLocalDb implements IAddWaterLocalDb {
  final Box<WaterIntake> _box;

  AddWaterLocalDb(this._box);

  @override
  Future<void> addWaterIntake(WaterIntake waterIntake) async {
    _box.put(waterIntake);
  }

  @override
  Future<List<WaterIntake>> getWaterIntake() async {
    return _box.getAll();
  }

  @override
  Future<void> deleteWaterIntake(WaterIntake waterIntake) async {
    _box.remove(waterIntake.id);
  }
}
