import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:lifeitude/constraints/app_text_style.dart';
import 'package:lifeitude/widgets/my_appbar.dart';
import 'package:watch_it/watch_it.dart';

import '../../../../components/water_intake_slider.dart';
import '../../../../constraints/app_colours.dart';
import '../../../../constraints/app_padding_only.dart';
import 'notifiers/add_water_notifier.dart';

class AddWaterScreen extends StatelessWidget with WatchItMixin {
  const AddWaterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final waterIntake = watchIt<AddWaterNotifier>();
    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: const MyAppBar(
        dontShowProfileIcon: true,
        title: 'Add Water',
        canPop: true,
        //backgroundColor: AppColours.appPurple,
      ),
      body: Padding(
        padding: const EdgeInsets.only(
          left: AppPaddingOnly.leftRight,
          right: AppPaddingOnly.leftRight,
          top: AppPaddingOnly.top,
        ),
        child: Column(
          children: [
            const SizedBox(height: 10.0),
            WaterIntakeSlider(
              minValue: 0.2,
              maxValue: 1.0,
              currentValue: waterIntake.currentValue,
              height: 70,
              width: MediaQuery.of(context).size.width,
              onChanged: (value) {
                debugPrint(value.toString());
                di<AddWaterNotifier>().setCurrentValue(value);
              },
            ),
            const SizedBox(height: 10),
            const Text("Or", style: AppTextStyle.bodyText),
            const SizedBox(height: 5),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                InkWell(
                  onTap: () {
                    di<AddWaterNotifier>().setCurrentValue(0.33);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/300mlicon.png', width: 50),
                      const Text('330ml', style: AppTextStyle.textSmall),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {
                    di<AddWaterNotifier>().setCurrentValue(0.5);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/500mlicon.png', width: 50),
                      const Text('500ml', style: AppTextStyle.textSmall),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {
                    di<AddWaterNotifier>().setCurrentValue(0.75);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/750mlicon.png', width: 50),
                      const Text('750ml', style: AppTextStyle.textSmall),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {
                    di<AddWaterNotifier>().setCurrentValue(1.0);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/1L0mlicon.png', width: 50),
                      const Text('1L', style: AppTextStyle.textSmall),
                    ],
                  ),
                ),
              ],
            ),
            TextButton(
              onPressed: () {
                di<AddWaterNotifier>().addWaterIntake();
              },
              style: TextButton.styleFrom(
                backgroundColor: AppColours.appOrgange,
                padding: const EdgeInsets.only(left: 10, right: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text('Add Water', style: AppTextStyle.textButton),
            ),
            const SizedBox(height: 10),
            Text(
              "You drunk ${waterIntake.currentValue.toStringAsFixed(1)}L is ${waterIntake.dailyIntakePercentage.toStringAsFixed(0)}% of",
              style: AppTextStyle.bodyText,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "your daily target of ${waterIntake.dailyIntake}L",
                  style: AppTextStyle.bodyText,
                ),
                const SizedBox(width: 15),
                const Text("Edit", style: AppTextStyle.bodyText),
                IconButton(
                  onPressed: () {
                    //

                    
                  },
                  icon: const Icon(
                    Icons.edit,
                    color: AppColours.appOrgange,
                    size: 30,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.only(bottom: 100),
                itemCount: waterIntake.waterIntakeList.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    leading: Image.asset(
                      'assets/images/water-drop-icon.png',
                      width: 50,
                    ),
                    title: Text(
                      'You drank ${waterIntake.waterIntakeList[index].amount.toStringAsFixed(1)}L',
                      style: AppTextStyle.bodyText,
                    ),
                    subtitle: Text(
                      'on ${DateFormat('dd/MM/yyyy HH:mm').format(waterIntake.waterIntakeList[index].date)}',
                      style: AppTextStyle.bodyText,
                    ),
                    trailing: IconButton(
                      icon: const Icon(
                        Icons.delete,
                        color: AppColours.appOrgange,
                        size: 30,
                      ),
                      onPressed: () {
                        di<AddWaterNotifier>().deleteWaterIntake(
                          waterIntake.waterIntakeList[index],
                        );
                      },
                    ),
                  );
                },
                shrinkWrap: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
