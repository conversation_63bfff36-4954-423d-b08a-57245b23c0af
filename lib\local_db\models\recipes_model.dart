class RecipesModel {
  Recipes? recipes;

  RecipesModel({this.recipes});

  RecipesModel.fromJson(Map<String, dynamic> json) {
    recipes = json['recipes'] != null
        ? Recipes.fromJson(json['recipes'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (recipes != null) {
      data['recipes'] = recipes!.toJson();
    }
    return data;
  }
}

class Recipes {
  String? maxResults;
  String? pageNumber;
  List<Recipe>? recipe;
  String? totalResults;

  Recipes({this.maxResults, this.pageNumber, this.recipe, this.totalResults});

  Recipes.fromJson(Map<String, dynamic> json) {
    maxResults = json['max_results'];
    pageNumber = json['page_number'];
    if (json['recipe'] != null) {
      recipe = <Recipe>[];
      json['recipe'].forEach((v) {
        recipe!.add(Recipe.fromJson(v));
      });
    }
    totalResults = json['total_results'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['max_results'] = maxResults;
    data['page_number'] = pageNumber;
    if (recipe != null) {
      data['recipe'] = recipe!.map((v) => v.toJson()).toList();
    }
    data['total_results'] = totalResults;
    return data;
  }
}

class Recipe {
  String? recipeDescription;
  String? recipeId;
  String? recipeImage;
  RecipeIngredients? recipeIngredients;
  String? recipeName;
  RecipeNutrition? recipeNutrition;
  RecipeTypes? recipeTypes;

  Recipe({
    this.recipeDescription,
    this.recipeId,
    this.recipeImage,
    this.recipeIngredients,
    this.recipeName,
    this.recipeNutrition,
    this.recipeTypes,
  });

  Recipe.fromJson(Map<String, dynamic> json) {
    recipeDescription = json['recipe_description'];
    recipeId = json['recipe_id'];
    recipeImage = json['recipe_image'];
    recipeIngredients = json['recipe_ingredients'] != null
        ? RecipeIngredients.fromJson(json['recipe_ingredients'])
        : null;
    recipeName = json['recipe_name'];
    recipeNutrition = json['recipe_nutrition'] != null
        ? RecipeNutrition.fromJson(json['recipe_nutrition'])
        : null;
    recipeTypes = json['recipe_types'] != null
        ? RecipeTypes.fromJson(json['recipe_types'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recipe_description'] = recipeDescription;
    data['recipe_id'] = recipeId;
    data['recipe_image'] = recipeImage;
    if (recipeIngredients != null) {
      data['recipe_ingredients'] = recipeIngredients!.toJson();
    }
    data['recipe_name'] = recipeName;
    if (recipeNutrition != null) {
      data['recipe_nutrition'] = recipeNutrition!.toJson();
    }
    if (recipeTypes != null) {
      data['recipe_types'] = recipeTypes!.toJson();
    }
    return data;
  }
}

class RecipeIngredients {
  List<String>? ingredient;

  RecipeIngredients({this.ingredient});

  RecipeIngredients.fromJson(Map<String, dynamic> json) {
    ingredient = json['ingredient'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ingredient'] = ingredient;
    return data;
  }
}

class RecipeNutrition {
  String? calories;
  String? carbohydrate;
  String? fat;
  String? protein;

  RecipeNutrition({this.calories, this.carbohydrate, this.fat, this.protein});

  RecipeNutrition.fromJson(Map<String, dynamic> json) {
    calories = json['calories'];
    carbohydrate = json['carbohydrate'];
    fat = json['fat'];
    protein = json['protein'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['calories'] = calories;
    data['carbohydrate'] = carbohydrate;
    data['fat'] = fat;
    data['protein'] = protein;
    return data;
  }
}

class RecipeTypes {
  List<String>? recipeType;

  RecipeTypes({this.recipeType});

  RecipeTypes.fromJson(Map<String, dynamic> json) {
    recipeType = json['recipe_type'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recipe_type'] = recipeType;
    return data;
  }
}
