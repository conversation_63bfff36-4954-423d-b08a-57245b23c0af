import 'package:flutter/material.dart';

class DashboardController extends ChangeNotifier {
  void init() {
    // Add your initialization code here
    debugPrint('DashboardController initialized');
  }

  int get dailyCalorieIntake => _dailyCalorieIntake;

  int _dailyCalorieIntake = 2000;

  void setDailyCalorieIntake(int value) {
    _dailyCalorieIntake = value;
    notifyListeners();
  }


  

}
