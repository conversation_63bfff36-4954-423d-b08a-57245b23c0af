import 'package:go_router/go_router.dart';

import '../screens/add_food_screen/add_food_screen.dart';
import '../screens/explore/add_friend_screen.dart';
import '../screens/main/explore_main/recipe_details_screen.dart';
import '../screens/main/log_screens_main/weight_screens/enter_weight_screen.dart';
import '../screens/main/log_screens_main/workout_screen/add_workout_screen.dart';
import '../screens/main/main_screen.dart';
import '../screens/main/workout_main/schedule_vr/schedule_vr_workout.dart';
import '../screens/profile_settings/profile_setting_screen.dart';
import '../screens/main/log_screens_main/water_screens/add_water_screen.dart';
import '../screens/reports/reports_screen.dart';
import '../screens/main/explore_main/recipe_search_screen.dart';
import 'analytics_route_observer.dart';

final GoRouter router = GoRouter(
  observers: [AnalyticsRouteObserver()],
  debugLogDiagnostics: false,
  initialLocation: '/',
  routes: [
    GoRoute(path: '/', builder: (context, state) => const MainScreen()),
    GoRoute(
      path: '/profileSettingScreen',
      builder: (context, state) => const ProfileSettingScreen(),
    ),

    GoRoute(
      path: '/addWaterScreen',
      builder: (context, state) => const AddWaterScreen(),
    ),
    GoRoute(
      path: '/addFriendScreen',
      builder: (context, state) => const AddFriendScreen(),
    ),
    GoRoute(
      path: '/addWorkoutScreen',
      builder: (context, state) => const AddWorkoutScreen(),
    ),
    GoRoute(
      path: '/scheduleVrWorkout',
      builder: (context, state) => const ScheduleVrWorkout(),
    ),
    GoRoute(
      path: '/enterWeightScreen',
      builder: (context, state) => const EnterWeightScreen(),
    ),
    GoRoute(
      path: '/reportsScreen',
      builder: (context, state) => const ReportsScreen(),
    ),
    GoRoute(
      path: '/recipeSearch',
      builder: (context, state) {
        final recipeType = state.uri.queryParameters['recipeType'] ?? '';
        return RecipeSearchScreen(recipeType: recipeType);
      },
    ),
    GoRoute(
      path: '/recipeDetails',

      builder: (context, state) {
        final recipeId = state.uri.queryParameters['recipeId'] ?? '';
        return RecipeDetailsScreen(recipeId: recipeId);
      },
    ),
    GoRoute(
      path: '/addFoodScreen',
      builder: (context, state) {
        final foodTime = state.uri.queryParameters['foodTime'] ?? '';
        return AddFoodScreen(foodTime: foodTime);
      },
    ),
  ],
);
