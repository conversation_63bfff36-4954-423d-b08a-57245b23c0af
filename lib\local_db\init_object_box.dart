import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

import '../locator/injection_container.dart';
import '../objectbox.g.dart';

Future<void> initObjectBox() async {
  final docsDir = await getApplicationDocumentsDirectory();
  final dbPath = path.join(docsDir.path, "objectbox");

  final store = Store(
    getObjectBoxModel(),
    directory: dbPath,
  );

  initializeDependencies(store);
}
