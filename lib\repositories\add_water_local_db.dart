import '../local_db/models/water_intake.dart';
import '../objectbox.g.dart';
import 'i_add_water_local_db.dart';

class AddWaterLocalDb implements IAddWaterLocalDb {
  final Box<WaterIntake> _box;

  AddWaterLocalDb(this._box);

  @override
  Future<void> addWaterIntake(WaterIntake waterIntake) async {
    _box.put(waterIntake);
  }

  @override
  Future<List<WaterIntake>> getWaterIntake() async {
    return _box.getAll();
  }

  @override
  Future<List<WaterIntake>> getTodaysWaterIntake() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

    // Create ObjectBox query to filter by today's date
    final query = _box
        .query(
          WaterIntake_.date.greaterOrEqual(todayStart.millisecondsSinceEpoch) &
              WaterIntake_.date.lessOrEqual(todayEnd.millisecondsSinceEpoch),
        )
        .build();

    final results = query.find();
    query.close();

    return results;
  }

  @override
  Future<void> deleteWaterIntake(WaterIntake waterIntake) async {
    _box.remove(waterIntake.id);
  }
}
