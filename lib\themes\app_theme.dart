import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../constraints/app_colours.dart';

class AppTheme {
  // Light Theme
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,

    // Custom Color Scheme for Light Theme
    colorScheme: const ColorScheme.light(
      primary: AppColours.appGreen,
      onPrimary: AppColours.appWhite,
      primaryContainer: AppColours.appBGgreen,
      onPrimaryContainer: AppColours.appGreenDarker,

      secondary: AppColours.appOrgange,
      onSecondary: AppColours.appWhite,
      secondaryContainer: Color(0xFFFFE8D6),
      onSecondaryContainer: Color(0xFF8B4000),

      tertiary: AppColours.appPurple,
      onTertiary: AppColours.appWhite,
      tertiaryContainer: Color(0xFFE8D5FF),
      onTertiaryContainer: Color(0xFF3D0080),

      error: Color(0xFFBA1A1A),
      onError: AppColours.appWhite,
      errorContainer: Color(0xFFFFDAD6),
      onErrorContainer: Color(0xFF410002),
      surface: AppColours.appWhite,
      onSurface: Color(0xFF1A1C18),
      surfaceContainerHighest: Color(0xFFE0E4D6),
      onSurfaceVariant: Color(0xFF43483E),

      outline: Color(0xFF74796D),
      outlineVariant: Color(0xFFC4C8BB),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFF2F312D),
      onInverseSurface: Color(0xFFF1F1EA),
      inversePrimary: AppColours.appBGgreen,
    ),

    // App Bar Theme
    appBarTheme: AppBarTheme(
      backgroundColor: AppColours.appGreen,
      foregroundColor: AppColours.appWhite,
      elevation: 0,
      centerTitle: true,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      titleTextStyle: TextStyle(
        color: AppColours.appWhite,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    // Card Theme
    cardTheme: CardThemeData(
      color: AppColours.appWhite,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),

    // Elevated Button Theme
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColours.appGreen,
        foregroundColor: AppColours.appWhite,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    ),

    // Text Button Theme
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColours.appGreen,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    ),

    // Outlined Button Theme
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColours.appGreen,
        side: const BorderSide(color: AppColours.appGreen, width: 1.5),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    ),

    // Input Decoration Theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColours.appBGgreen,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColours.appGreen),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: AppColours.appGreen.withValues(alpha: 0.5),
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColours.appGreen, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
      labelStyle: const TextStyle(color: AppColours.appGreenDark),
      hintStyle: TextStyle(
        color: AppColours.appGreenDark.withValues(alpha: 0.6),
      ),
    ),

    // Floating Action Button Theme
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColours.appOrgange,
      foregroundColor: AppColours.appWhite,
      elevation: 4,
    ),

    // Bottom Navigation Bar Theme
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColours.appWhite,
      selectedItemColor: AppColours.appGreen,
      unselectedItemColor: Colors.grey,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),

    // Chip Theme
    chipTheme: ChipThemeData(
      backgroundColor: AppColours.appBGgreen,
      selectedColor: AppColours.appGreen,
      labelStyle: const TextStyle(color: AppColours.appGreenDark),
      secondaryLabelStyle: const TextStyle(color: AppColours.appWhite),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),

    // Divider Theme
    dividerTheme: const DividerThemeData(
      color: Colors.grey,
      thickness: 1,
      space: 1,
    ),
  );

  // Dark Theme
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,

    // Custom Color Scheme for Dark Theme
    colorScheme: const ColorScheme.dark(
      primary: AppColours.appGreen,
      onPrimary: AppColours.appGreenDarker,
      primaryContainer: AppColours.appGreenDark,
      onPrimaryContainer: AppColours.appBGgreen,

      secondary: AppColours.appOrgange,
      onSecondary: Color(0xFF8B4000),
      secondaryContainer: Color(0xFF8B4000),
      onSecondaryContainer: Color(0xFFFFE8D6),

      tertiary: AppColours.appPurple,
      onTertiary: Color(0xFF3D0080),
      tertiaryContainer: Color(0xFF3D0080),
      onTertiaryContainer: Color(0xFFE8D5FF),

      error: Color(0xFFFFB4AB),
      onError: Color(0xFF690005),
      errorContainer: Color(0xFF93000A),
      onErrorContainer: Color(0xFFFFDAD6),

      surface: Color(0xFF111411),
      onSurface: Color(0xFFE1E3DC),
      surfaceContainerHighest: Color(0xFF3A3F35),
      onSurfaceVariant: Color(0xFFC4C8BB),

      outline: Color(0xFF8E9387),
      outlineVariant: Color(0xFF43483E),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFFE1E3DC),
      onInverseSurface: Color(0xFF2F312D),
      inversePrimary: AppColours.appGreenDark,
    ),

    // App Bar Theme
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColours.appGreenDarker,
      foregroundColor: AppColours.appWhite,
      elevation: 0,
      centerTitle: true,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      titleTextStyle: TextStyle(
        color: AppColours.appWhite,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    // Card Theme
    cardTheme: CardThemeData(
      color: Color(0xFF1E201C),
      shadowColor: Colors.black.withValues(alpha: 0.3),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),

    // Elevated Button Theme
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColours.appGreen,
        foregroundColor: AppColours.appGreenDarker,
        elevation: 3,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    ),

    // Text Button Theme
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColours.appGreen,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    ),

    // Outlined Button Theme
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColours.appGreen,
        side: const BorderSide(color: AppColours.appGreen, width: 1.5),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    ),

    // Input Decoration Theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColours.appGreenDarker,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColours.appGreen),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: AppColours.appGreen.withValues(alpha: 0.7),
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColours.appGreen, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.redAccent),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.redAccent, width: 2),
      ),
      labelStyle: const TextStyle(color: AppColours.appGreen),
      hintStyle: TextStyle(color: AppColours.appGreen.withValues(alpha: 0.6)),
    ),

    // Floating Action Button Theme
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColours.appOrgange,
      foregroundColor: AppColours.appWhite,
      elevation: 6,
    ),

    // Bottom Navigation Bar Theme
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF1E201C),
      selectedItemColor: AppColours.appGreen,
      unselectedItemColor: Colors.grey,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),

    // Chip Theme
    chipTheme: ChipThemeData(
      backgroundColor: AppColours.appGreenDarker,
      selectedColor: AppColours.appGreen,
      labelStyle: const TextStyle(color: AppColours.appWhite),
      secondaryLabelStyle: const TextStyle(color: AppColours.appGreenDarker),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),

    // Divider Theme
    dividerTheme: const DividerThemeData(
      color: Colors.grey,
      thickness: 1,
      space: 1,
    ),
  );
}
