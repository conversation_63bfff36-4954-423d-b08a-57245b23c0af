import 'package:flutter/material.dart';
import 'package:lifeitude/screens/main/explore_main/tabs_screens/chat_screen.dart';

import '../../../constraints/app_colours.dart';
import '../../../widgets/my_appbar.dart';
import 'tabs_screens/friends_screen.dart';
import 'tabs_screens/news_screen.dart';
import 'tabs_screens/recipes_screen.dart';

class ExploreScreen extends StatelessWidget {
  const ExploreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: MyAppBar(title: 'Explore', canPop: false),
      body: DefaultTabController(
        length: 3,
        initialIndex: 0,
        child: Column(
          children: [
            TabBar(
              padding: EdgeInsets.only(left: 0.00, right: 0.00),
              tabs: [
                SizedBox(
                  width: double.infinity,
                  child: Tab(
                    child: Text("Chat", style: TextStyle(fontSize: 18)),
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: Tab(
                    child: Text(style: TextStyle(fontSize: 18), "Recipes"),
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: Tab(
                    child: Text("News", style: TextStyle(fontSize: 18)),
                  ),
                ),
              ],
            ),
            Expanded(
              child: TabBarView(
                physics: NeverScrollableScrollPhysics(),
                children: [ChatScreen(), RecipesScreen(), NewsScreen()],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
