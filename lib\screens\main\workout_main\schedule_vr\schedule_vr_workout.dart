import 'package:date_time_picker/date_time_picker.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:lifeitude/constraints/app_text_style.dart';
import 'package:watch_it/watch_it.dart';

import '../../../../constraints/app_colours.dart';
import '../../../../constraints/app_padding_only.dart';
import '../../../../constraints/design_stuff.dart';
import '../../../../widgets/my_appbar.dart';
import '../../../explore/notifiers/add_friend_notifier.dart';

class ScheduleVrWorkout extends WatchingStatefulWidget {
  const ScheduleVrWorkout({super.key});

  @override
  State<ScheduleVrWorkout> createState() => _ScheduleVrWorkoutState();
}

class _ScheduleVrWorkoutState extends State<ScheduleVrWorkout> {
  //final List<DateTime> _dates = [];

  DateTime _selectedDate = DateTime.now();

  bool showAddFriends = false;

  @override
  void initState() {
    super.initState();
    showAddFriends = false;
  }

  @override
  Widget build(BuildContext context) {
    final friendsList = watchIt<AddFriendNotifier>();
    return Scaffold(
      appBar: const MyAppBar(
        dontShowProfileIcon: true,
        title: 'Schedule VR Workout',
        canPop: true,
        //backgroundColor: AppColours.appPurple,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
                left: AppPaddingOnly.leftRight,
                right: AppPaddingOnly.leftRight,
                top: AppPaddingOnly.top),
            child: DateTimePicker(
              type: DateTimePickerType.dateTime,
              initialValue: '',
              firstDate: DateTime(2000),
              lastDate: DateTime(2100),
              dateLabelText: 'Date',
              onChanged: (val) {
                setState(() {
                  _selectedDate = DateTime.parse(val);
                  debugPrint("Selected Date: $_selectedDate");
                  showAddFriends = true;
                });
              },
              onSaved: (val) {
                setState(() {
                  _selectedDate = DateTime.parse(val!);
                  showAddFriends = true;
                });
              },
            ),
          ),
          const SizedBox(
            height: 10.0,
          ),
          showAddFriends
              ? Text(
                  "Date picked ${DateFormat('dd/MM/yyyy kk:mm').format(_selectedDate)}",
                  style: AppTextStyle.bodyText)
              : const SizedBox.shrink(),
          // Text(
          //   "Selected Dates: ${_dates.map((date) => "${date.day}/${date.month}/${date.year}").join(", ")}",
          //   style: AppTextStyle.bodyText,
          // ),
          showAddFriends
              ? ListTile(
                  title: const Text(
                    "Add Friends",
                    style: AppTextStyle.bodyText,
                  ),
                  trailing: const Icon(
                    Icons.add,
                    color: AppColours.appPurple,
                    size: 30,
                  ),
                  onTap: () {
                    // Add friends
                    showModalBottomSheet(
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      context: context,
                      builder: (BuildContext context) {
                        return Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(18),
                            ),
                          ),
                          height: MediaQuery.of(context).size.height * 0.8,
                          width: double.infinity,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.close,
                                          color: Colors.black),
                                      onPressed: () {
                                        // Close the bottom sheet
                                        Navigator.of(context).pop();
                                      },
                                    ),
                                  ],
                                ),
                                Expanded(
                                  child: Center(
                                    child: ListView.builder(
                                      itemCount: friendsList.friends.length,
                                      itemBuilder: (context, index) {
                                        return Card(
                                          elevation: DesignStuff.cardElevation,
                                          child: ListTile(
                                            leading: const CircleAvatar(
                                              backgroundImage: NetworkImage(
                                                "https://www.gravatar.com/avatar/2c7d99fe281ecd3bcd65ab915bac6dd5?s=250",
                                              ),
                                            ),
                                            title: Text(
                                              friendsList.friends[index].name,
                                              style: AppTextStyle.bodyText,
                                            ),
                                            subtitle: Text(
                                              friendsList.friends[index].email,
                                              style: AppTextStyle.bodyText,
                                            ),
                                            trailing: IconButton(
                                              icon: const Icon(
                                                size: 30.0,
                                                Icons.add,
                                                color: AppColours.appPurple,
                                              ),
                                              onPressed: () {
                                                di<AddFriendNotifier>()
                                                    .addFriendToVR(
                                                  friendsList.friends[index].id,
                                                  friendsList
                                                      .friends[index].name,
                                                  friendsList
                                                      .friends[index].email,
                                                  _selectedDate,
                                                );
                                              },
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                )
              : const SizedBox.shrink(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(
                  left: AppPaddingOnly.leftRight,
                  right: AppPaddingOnly.leftRight,
                  top: AppPaddingOnly.top),
              child: ListView.builder(
                itemCount: friendsList.vrFriends.length,
                itemBuilder: (context, index) {
                  return Dismissible(
                    key: UniqueKey(),
                    onDismissed: (direction) {
                      di<AddFriendNotifier>().removeVrFriend(index);
                    },
                    child: Card(
                      elevation: DesignStuff.cardElevation,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            Text(
                                "Booked: ${DateFormat('dd/MM/yyyy kk:mm').format(friendsList.vrFriends[index].date)}",
                                style: AppTextStyle.cardHeaderText),
                            Text(
                                "With: ${friendsList.vrFriends.map((friend) => friend.name).join(", ")}",
                                style: AppTextStyle.bodyText),
                          ],
                        ),
                      ),
                    ),
                  );
                },
                shrinkWrap: true,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
