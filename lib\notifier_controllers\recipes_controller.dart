import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../local_db/models/recipes_model.dart';
import '../repositories/i_backend_api.dart';

class RecipesController extends ChangeNotifier {
  RecipesController() {
    init();
  }

  List<String> _recipesTypes = [];
  List<String> get recipesTypes => _recipesTypes;

  bool _isloading = false;
  bool get isLoading => _isloading;

  List<Recipes>? _recipes;
  List<Recipes>? get recipes => _recipes;

  Future<void> init() async {
    debugPrint('RecipesController initialized');

    await getRecipes();
  }

  Future<void> getRecipes() async {
    _isloading = true;
    notifyListeners();

    final response = await di<IBackendApi>().getResipesTypes();

    Map<String, dynamic> responseData;
    if (response.data is String) {
      responseData =
          jsonDecode(response.data as String) as Map<String, dynamic>;
    } else {
      responseData = response.data as Map<String, dynamic>;
    }

    final Map<String, dynamic> recipeTypes =
        responseData['recipe_types'] as Map<String, dynamic>;

    _recipesTypes = (recipeTypes['recipe_type'] as List<dynamic>)
        .cast<String>()
        .toList();

    _isloading = false;
    notifyListeners();
  }

  Future<void> getRecipesByType(String recipeType) async {
    _isloading = true;
    notifyListeners();

    final response = await di<IBackendApi>().getRecipesByType(recipeType);

    Map<String, dynamic> responseData;
    if (response.data is String) {
      responseData =
          jsonDecode(response.data as String) as Map<String, dynamic>;
    } else {
      responseData = response.data as Map<String, dynamic>;
    }

    _recipes = (responseData['recipes'] as List<dynamic>)
        .map((e) => RecipesModel.fromJson(e))
        .toList();

    debugPrint(_recipes![0].recipes!.toString());

    _isloading = false;
    notifyListeners();
  }
}
