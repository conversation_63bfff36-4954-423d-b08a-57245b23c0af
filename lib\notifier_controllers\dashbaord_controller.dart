import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_daily_calories_intake.dart';

class DashboardController extends ChangeNotifier {
  DashboardController() {
    init();
  }

  Future<void> init() async {
    debugPrint('DashboardController initialized');
    // Load today's calorie intake from database
    await _loadTodaysCalorieIntake();
  }

  int get dailyCalorieIntake => _dailyCalorieIntake;

  int _dailyCalorieIntake = 2000;

  Future<void> _loadTodaysCalorieIntake() async {
    try {
      final todaysCalories = await di<IDailyCaloriesIntake>()
          .getDailyCaloriesIntake();
      _dailyCalorieIntake = todaysCalories;
      debugPrint(
        'Loaded today\'s calorie intake from database: $_dailyCalorieIntake',
      );
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading today\'s calorie intake: $e');
    }
  }

  Future<void> setDailyCalorieIntake(int value) async {
    _dailyCalorieIntake = value;

    debugPrint('Daily calorie intake set to: $_dailyCalorieIntake');

    try {
      // Use the new method that handles: check -> delete -> save
      await di<IDailyCaloriesIntake>().setTodaysCalorieIntake(value);
      debugPrint('✅ Successfully saved today\'s calorie intake to database');
    } catch (e) {
      debugPrint('❌ Error saving calorie intake: $e');
    }

    notifyListeners();
  }
}
