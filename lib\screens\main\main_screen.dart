import 'package:flutter/material.dart';

import '../../constraints/app_icons_sizes.dart';
import 'dashboard/dashboard_screen.dart';
import 'explore_main/explore_screen.dart';
import 'log_screens_main/log_main_screen.dart';
import 'workout_main/workout_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final PageController _pageController = PageController(
    initialPage: 0,
  );
  int currentPage = 0;

  @override
  void initState() {
    super.initState();
    currentPage = 0;
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        physics: const NeverScrollableScrollPhysics(),
        onPageChanged: (index) {
          setState(() {
            currentPage = index;
          });
        },
        controller: _pageController,
        children: const [
          Dashboard(),
          LogMainScreen(),
          WorkoutScreen(),
          ExploreScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: currentPage,
        onTap: (index) {
          setState(() {
            currentPage = index;
          });

          _pageController.jumpToPage(index);
          // _pageController.animateToPage(
          //   index,
          //   duration: const Duration(milliseconds: 300),
          //   curve: Curves.easeInOut,
          // );
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(
              Icons.space_dashboard,
              size: AppIconsSizes.navBarItem,
            ),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(
              Icons.assignment_add,
              size: AppIconsSizes.navBarItem,
            ),
            label: 'Log',
          ),
          BottomNavigationBarItem(
            icon: Icon(
              Icons.fitness_center,
              size: AppIconsSizes.navBarItem,
            ),
            label: 'Workout',
          ),
          BottomNavigationBarItem(
            icon: Icon(
              Icons.explore,
              size: AppIconsSizes.navBarItem,
            ),
            label: 'Explore',
          ),
        ],
      ),
    );
  }
}
