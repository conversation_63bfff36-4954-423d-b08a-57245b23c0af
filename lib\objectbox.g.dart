// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'local_db/models/daily_calories_intake.dart';
import 'local_db/models/user_model.dart';
import 'local_db/models/water_intake.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
    id: const obx_int.IdUid(1, 7615383821406597893),
    name: 'User',
    lastPropertyId: const obx_int.IdUid(4, 7859629875939235061),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 4445577906741154521),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 7160566873683919576),
        name: 'name',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 4266825367440328609),
        name: 'email',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 7859629875939235061),
        name: 'googleID',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(2, 8327886063272642654),
    name: 'WaterIntake',
    lastPropertyId: const obx_int.IdUid(3, 3774334434446824438),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 1491734986379840104),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 7853589791053821168),
        name: 'amount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 3774334434446824438),
        name: 'date',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(3, 2282790803507039331),
    name: 'DailyCaloriesIntake',
    lastPropertyId: const obx_int.IdUid(3, 8211179075246980368),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 6538585680689791928),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 5431266356971654758),
        name: 'calories',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 8211179075246980368),
        name: 'date',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore({
  String? directory,
  int? maxDBSizeInKB,
  int? maxDataSizeInKB,
  int? fileMode,
  int? maxReaders,
  bool queriesCaseSensitiveDefault = true,
  String? macosApplicationGroup,
}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(
    getObjectBoxModel(),
    directory: directory ?? (await defaultStoreDirectory()).path,
    maxDBSizeInKB: maxDBSizeInKB,
    maxDataSizeInKB: maxDataSizeInKB,
    fileMode: fileMode,
    maxReaders: maxReaders,
    queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
    macosApplicationGroup: macosApplicationGroup,
  );
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
    entities: _entities,
    lastEntityId: const obx_int.IdUid(3, 2282790803507039331),
    lastIndexId: const obx_int.IdUid(0, 0),
    lastRelationId: const obx_int.IdUid(0, 0),
    lastSequenceId: const obx_int.IdUid(0, 0),
    retiredEntityUids: const [],
    retiredIndexUids: const [],
    retiredPropertyUids: const [],
    retiredRelationUids: const [],
    modelVersion: 5,
    modelVersionParserMinimum: 5,
    version: 1,
  );

  final bindings = <Type, obx_int.EntityDefinition>{
    User: obx_int.EntityDefinition<User>(
      model: _entities[0],
      toOneRelations: (User object) => [],
      toManyRelations: (User object) => {},
      getId: (User object) => object.id,
      setId: (User object, int id) {
        object.id = id;
      },
      objectToFB: (User object, fb.Builder fbb) {
        final nameOffset = fbb.writeString(object.name);
        final emailOffset = fbb.writeString(object.email);
        final googleIDOffset = fbb.writeString(object.googleID);
        fbb.startTable(5);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, nameOffset);
        fbb.addOffset(2, emailOffset);
        fbb.addOffset(3, googleIDOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final nameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final emailParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final googleIDParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final object = User(
          name: nameParam,
          email: emailParam,
          googleID: googleIDParam,
        )..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
    WaterIntake: obx_int.EntityDefinition<WaterIntake>(
      model: _entities[1],
      toOneRelations: (WaterIntake object) => [],
      toManyRelations: (WaterIntake object) => {},
      getId: (WaterIntake object) => object.id,
      setId: (WaterIntake object, int id) {
        object.id = id;
      },
      objectToFB: (WaterIntake object, fb.Builder fbb) {
        fbb.startTable(4);
        fbb.addInt64(0, object.id);
        fbb.addFloat64(1, object.amount);
        fbb.addInt64(2, object.date.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final amountParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          6,
          0,
        );
        final dateParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 8, 0),
        );
        final object = WaterIntake(
          id: idParam,
          amount: amountParam,
          date: dateParam,
        );

        return object;
      },
    ),
    DailyCaloriesIntake: obx_int.EntityDefinition<DailyCaloriesIntake>(
      model: _entities[2],
      toOneRelations: (DailyCaloriesIntake object) => [],
      toManyRelations: (DailyCaloriesIntake object) => {},
      getId: (DailyCaloriesIntake object) => object.id,
      setId: (DailyCaloriesIntake object, int id) {
        object.id = id;
      },
      objectToFB: (DailyCaloriesIntake object, fb.Builder fbb) {
        fbb.startTable(4);
        fbb.addInt64(0, object.id);
        fbb.addInt64(1, object.calories);
        fbb.addInt64(2, object.date.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final caloriesParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          6,
          0,
        );
        final dateParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 8, 0),
        );
        final object = DailyCaloriesIntake(
          id: idParam,
          calories: caloriesParam,
          date: dateParam,
        );

        return object;
      },
    ),
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [User] entity fields to define ObjectBox queries.
class User_ {
  /// See [User.id].
  static final id = obx.QueryIntegerProperty<User>(_entities[0].properties[0]);

  /// See [User.name].
  static final name = obx.QueryStringProperty<User>(_entities[0].properties[1]);

  /// See [User.email].
  static final email = obx.QueryStringProperty<User>(
    _entities[0].properties[2],
  );

  /// See [User.googleID].
  static final googleID = obx.QueryStringProperty<User>(
    _entities[0].properties[3],
  );
}

/// [WaterIntake] entity fields to define ObjectBox queries.
class WaterIntake_ {
  /// See [WaterIntake.id].
  static final id = obx.QueryIntegerProperty<WaterIntake>(
    _entities[1].properties[0],
  );

  /// See [WaterIntake.amount].
  static final amount = obx.QueryDoubleProperty<WaterIntake>(
    _entities[1].properties[1],
  );

  /// See [WaterIntake.date].
  static final date = obx.QueryDateProperty<WaterIntake>(
    _entities[1].properties[2],
  );
}

/// [DailyCaloriesIntake] entity fields to define ObjectBox queries.
class DailyCaloriesIntake_ {
  /// See [DailyCaloriesIntake.id].
  static final id = obx.QueryIntegerProperty<DailyCaloriesIntake>(
    _entities[2].properties[0],
  );

  /// See [DailyCaloriesIntake.calories].
  static final calories = obx.QueryIntegerProperty<DailyCaloriesIntake>(
    _entities[2].properties[1],
  );

  /// See [DailyCaloriesIntake.date].
  static final date = obx.QueryDateProperty<DailyCaloriesIntake>(
    _entities[2].properties[2],
  );
}
