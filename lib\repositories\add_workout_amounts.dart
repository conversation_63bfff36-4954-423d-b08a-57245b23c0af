import '../local_db/models/workout_model.dart';
import '../objectbox.g.dart';
import 'i_add_workout_amounts.dart';

class AddWorkoutAmounts implements IAddWorkoutAmounts {
  final Box<WorkoutModel> _box;

  AddWorkoutAmounts(this._box);

  @override
  Future<void> addWorkoutAmount(int calories, String exerciseName) async {
    final newWorkoutAmount = WorkoutModel(
      id: 0,
      caloriesBurned: calories,
      date: DateTime.now(),
      exerciseName: exerciseName,
    );

    _box.put(newWorkoutAmount);
  }

  @override
  Future<List<WorkoutModel>> getWorkoutAmount() async {
    return _box.getAll();
  }


  @override
  Future<List<WorkoutModel>> getTodaysWorkout() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final query = _box
        .query(
          WorkoutModel_.date.greaterOrEqual(todayStart.millisecondsSinceEpoch) &
              WorkoutModel_.date.lessOrEqual(todayEnd.millisecondsSinceEpoch),
        )
        .build();

    final results = query.find();
    query.close();

    return results;
  }

  @override
  Future<void> deleteWorkoutAmount(WorkoutModel workoutAmount) async {
    _box.remove(workoutAmount.id);
  }

  @override
  Future<void> deleteTodaysWorkout() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final query = _box
        .query(
          WorkoutModel_.date.greaterOrEqual(todayStart.millisecondsSinceEpoch) &
              WorkoutModel_.date.lessOrEqual(todayEnd.millisecondsSinceEpoch),
        )
        .build();

    final results = query.find();
    query.close();

    for (final workout in results) {
      _box.remove(workout.id);
    }
  }


  @override
  Future<void> deleteAllWorkout() async {
    _box.removeAll();
  }
}
