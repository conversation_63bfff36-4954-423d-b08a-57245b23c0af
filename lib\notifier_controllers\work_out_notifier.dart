import 'package:flutter/material.dart';
import 'package:lifeitude/router/router.dart';
import 'package:watch_it/watch_it.dart';

import '../data/exercise_cist_temp_data.dart';
import '../local_db/models/exercies_list.dart';
import '../local_db/models/workout_model.dart';
import '../repositories/i_add_workout_amounts.dart';

class WorkOutNotifier extends ChangeNotifier {
  List<ExerciseList> _filteredExercises = ExerciseCistTempData.exerciseList;
  List<ExerciseList> get filteredExercises => _filteredExercises;

  int _totalCalories = 0;
  int get totalCalories => _totalCalories;

  String _exerciseName = '';
  String get exerciseName => _exerciseName;

  int _totalWorkoutForToday = 0;
  int get totalWorkoutForToday => _totalWorkoutForToday;

  WorkOutNotifier() {
    _filteredExercises.sort((a, b) => a.name.compareTo(b.name));
    init(); // Call init to load today's workout data
    notifyListeners();
  }

  Future<void> init() async {
    await getTodaysWorkoutLogs();
  }

  void resetExercises() {
    _filteredExercises = ExerciseCistTempData.exerciseList;
    notifyListeners();
  }

  void searchExercises(String query) {
    if (query.isEmpty) {
      _filteredExercises = ExerciseCistTempData.exerciseList;
    } else {
      _filteredExercises = ExerciseCistTempData.exerciseList
          .where(
            (exercise) =>
                exercise.name.toLowerCase().contains(query.toLowerCase()),
          )
          .toList();
    }
    notifyListeners();
  }

  Future<void> getTodaysWorkoutLogs() async {
    List<WorkoutModel> workoutList = await di<IAddWorkoutAmounts>()
        .getTodaysWorkout();

    _totalWorkoutForToday = workoutList.fold(
      0,
      (sum, workout) => sum + workout.caloriesBurned,
    );

    notifyListeners();
  }

  void setWorkoutTime({
    required int hours,
    required int minutes,
    required int totalCalories,
    required String exerciseName,
  }) {
    _totalCalories = totalCalories;
    _exerciseName = exerciseName;
    notifyListeners();
  }

  void resetWorkoutTime() {
    _totalCalories = 0;
    notifyListeners();
  }

  Future<void> saveWorkoutTime() async {
    await di<IAddWorkoutAmounts>().addWorkoutAmount(
      _totalCalories,
      _exerciseName,
    );

    debugPrint('Workout Time Saved - ${_totalCalories.toString()}');
    router.pop();
    router.pop();

    getTodaysWorkoutLogs();
  }
}
