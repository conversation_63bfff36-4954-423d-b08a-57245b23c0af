import 'package:flutter/material.dart';
import 'package:lifeitude/constraints/app_colours.dart';

class WeeklyBarChart extends StatelessWidget {
  // Input values for each day of the week
  final List<double> weeklyValues;

  // Optional total chart height
  final double? chartHeight;

  // Constructor to accept weekly values and optional chart height
  const WeeklyBarChart({
    super.key,
    required this.weeklyValues,
    this.chartHeight = 200, // Default height of 200
  });

  @override
  Widget build(BuildContext context) {
    // Validate input list
    assert(weeklyValues.length == 7, 'Must provide exactly 7 values');

    // Calculate the maximum value for percentage calculation
    const double maxValue = 2500;
    final days = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: List.generate(7, (index) {
        // Calculate percentage for each day
        double percentage = (weeklyValues[index] / maxValue).clamp(0.0, 1.0);

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Bar with dynamic height based on percentage and optional chart height
            Container(
              width: 30,
              height: chartHeight! * percentage,
              color: _getColorForPercentage(percentage),
            ),
            const SizedBox(height: 8),
            // Day label
            Text(
              days[index],
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        );
      }),
    );
  }

  // Color gradient based on percentage
  Color _getColorForPercentage(double percentage) {
    return Color.lerp(
        AppColours.appGreenDarker, AppColours.appGreen, percentage)!;
  }
}
