import 'package:flutter/material.dart';
import 'package:lifeitude/router/router.dart';

import '../../../../../data/exercise_cist_temp_data.dart';
import '../../../../../local_db/models/exercies_list.dart';

class WorkOutNotifier extends ChangeNotifier {
  List<ExerciseList> _filteredExercises = ExerciseCistTempData.exerciseList;
  List<ExerciseList> get filteredExercises => _filteredExercises;

  WorkOutNotifier() {
    _filteredExercises.sort((a, b) => a.name.compareTo(b.name));
    notifyListeners();
  }

  void resetExercises() {
    _filteredExercises = ExerciseCistTempData.exerciseList;
    notifyListeners();
  }

  void searchExercises(String query) {
    if (query.isEmpty) {
      _filteredExercises = ExerciseCistTempData.exerciseList;
    } else {
      _filteredExercises = ExerciseCistTempData.exerciseList
          .where((exercise) =>
              exercise.name.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
    notifyListeners();
  }

  int _totalCalories = 0;
  int get totalCalories => _totalCalories;

  void setWorkoutTime({
    required int hours,
    required int minutes,
    required int totalCalories,
  }) {
    _totalCalories = totalCalories;
    notifyListeners();
  }

  void resetWorkoutTime() {
    _totalCalories = 0;
    notifyListeners();
  }

  void saveWorkoutTime() {
    debugPrint('Workout Time Saved - ${_totalCalories.toString()}');
    router.pop();
    router.pop();
  }
}
