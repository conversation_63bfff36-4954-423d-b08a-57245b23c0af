{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:7615383821406597893", "lastPropertyId": "4:7859629875939235061", "name": "User", "properties": [{"id": "1:4445577906741154521", "name": "id", "type": 6, "flags": 1}, {"id": "2:7160566873683919576", "name": "name", "type": 9}, {"id": "3:4266825367440328609", "name": "email", "type": 9}, {"id": "4:7859629875939235061", "name": "googleID", "type": 9}], "relations": []}, {"id": "2:8327886063272642654", "lastPropertyId": "3:3774334434446824438", "name": "WaterIntake", "properties": [{"id": "1:1491734986379840104", "name": "id", "type": 6, "flags": 1}, {"id": "2:7853589791053821168", "name": "amount", "type": 8}, {"id": "3:3774334434446824438", "name": "date", "type": 10}], "relations": []}, {"id": "3:2282790803507039331", "lastPropertyId": "3:8211179075246980368", "name": "DailyCaloriesIntake", "properties": [{"id": "1:6538585680689791928", "name": "id", "type": 6, "flags": 1}, {"id": "2:5431266356971654758", "name": "calories", "type": 6}, {"id": "3:8211179075246980368", "name": "date", "type": 10}], "relations": []}, {"id": "4:2676651554685384774", "lastPropertyId": "3:8090768272324896754", "name": "WeightLogModel", "properties": [{"id": "1:6692532900521804936", "name": "id", "type": 6, "flags": 1}, {"id": "2:4350122299765191119", "name": "weight", "type": 8}, {"id": "3:8090768272324896754", "name": "date", "type": 10}], "relations": []}, {"id": "5:667640567216558140", "lastPropertyId": "4:5021470897664683957", "name": "WorkoutModel", "properties": [{"id": "1:6197865531431366812", "name": "id", "type": 6, "flags": 1}, {"id": "2:4300713815663778030", "name": "caloriesBurned", "type": 6}, {"id": "3:4798268810026043840", "name": "date", "type": 10}, {"id": "4:5021470897664683957", "name": "exercise<PERSON>ame", "type": 9}], "relations": []}], "lastEntityId": "5:667640567216558140", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}