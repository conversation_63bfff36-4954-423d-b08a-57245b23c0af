import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_backend_api.dart';

class RecipesController extends ChangeNotifier {
  RecipesController() {
    init();
  }

  Future<void> init() async {
    debugPrint('RecipesController initialized');

    await getRecipes();
  }

  Future<void> getRecipes() async {
    debugPrint('Getting recipes');

    final response = await di<IBackendApi>().getResipesTypes();

    debugPrint('Recipes: ${response.data}');
  }
}
