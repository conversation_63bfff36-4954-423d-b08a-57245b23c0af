import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifeitude/constraints/app_colours.dart';
import 'package:lifeitude/constraints/app_text_style.dart';
import 'package:lifeitude/widgets/my_appbar.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:watch_it/watch_it.dart';

import '../../constraints/app_padding_only.dart';
import 'notifiers/add_friend_notifier.dart';

class AddFriendScreen extends StatefulWidget {
  const AddFriendScreen({super.key});

  @override
  State<AddFriendScreen> createState() => _AddFriendScreenState();
}

class _AddFriendScreenState extends State<AddFriendScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MyAppBar(
        dontShowProfileIcon: true,
        canPop: true,
        title: 'Add Friend',
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.only(
              left: AppPaddingOnly.leftRight,
              right: AppPaddingOnly.leftRight,
              top: AppPaddingOnly.top),
          child: ListView(
            children: [
              const SizedBox(height: 20),
              TextFormField(
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
                keyboardType: TextInputType.name,
                controller: _nameController,
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.all(12),
                  hintText: 'Your friend\'s name',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              TextFormField(
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Please enter an email';
                  }
                  final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
                  if (!emailRegex.hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
                keyboardType: TextInputType.emailAddress,
                controller: _emailController,
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.all(12),
                  hintText: 'Enter Email',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColours.appOrgange,
                  padding: const EdgeInsets.all(8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    // Process data.

                    di<AddFriendNotifier>().setNameAndEmail(
                      _nameController.text.trim(),
                      _emailController.text.trim(),
                    );

                    FocusScope.of(context).unfocus();

                    context.pop();
                  }
                },
                child: Text(
                  'Add Friend',
                  style: AppTextStyle.buttonText.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              const Text(
                'If your friend is close to you and as this app, get them to scan this QR code to add you as a friend',
                style: AppTextStyle.bodyText,
              ),
              const SizedBox(
                height: 20,
              ),
              //TODO: Add QR code, needs to be the user app users id
              Center(
                child: QrImageView(
                  data: '1234567890',
                  version: QrVersions.auto,
                  size: 200.0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
