import 'package:objectbox/objectbox.dart';

import '../local_db/models/user_model.dart';

import 'package:watch_it/watch_it.dart';

import '../local_db/models/water_intake.dart';
import '../repositories/add_water_local_db.dart';
import '../repositories/i_add_water_local_db.dart';
import '../screens/explore/notifiers/add_friend_notifier.dart';
import '../screens/main/log_screens_main/water_screens/notifiers/add_water_notifier.dart';
import '../screens/main/log_screens_main/weight_screens/notifiers/add_weight_notifier.dart';
import '../screens/main/log_screens_main/workout_screen/notifiers/work_out_notifier.dart';

initializeDependencies(Store store) {
  di.registerSingleton<Store>(store);

  di.registerSingleton<Box<User>>(store.box<User>());

  di.registerLazySingleton<AddFriendNotifier>(() => AddFriendNotifier());

  di.registerLazySingleton<WorkOutNotifier>(() => WorkOutNotifier());

  di.registerLazySingleton<AddWeightNotifier>(() => AddWeightNotifier());

  di.registerLazySingleton<Box<WaterIntake>>(() => store.box<WaterIntake>());

  di.registerLazySingleton<IAddWaterLocalDb>(
    () => AddWaterLocalDb(di<Box<WaterIntake>>()),
  );

  di.registerLazySingleton<AddWaterNotifier>(() => AddWaterNotifier());
}
