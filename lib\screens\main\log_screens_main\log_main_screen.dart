import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifeitude/constraints/app_text_style.dart';
import 'package:watch_it/watch_it.dart';

import '../../../constraints/app_colours.dart';
import '../../../constraints/app_padding_only.dart';
import '../../../constraints/design_stuff.dart';
import '../../../widgets/my_appbar.dart';
import 'workout_screen/notifiers/work_out_notifier.dart';

class LogMainScreen extends StatelessWidget {
  const LogMainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: const MyAppBar(title: 'Log Calories', canPop: false),
      body: Padding(
        padding: const EdgeInsets.only(
            left: AppPaddingOnly.leftRight,
            right: AppPaddingOnly.leftRight,
            top: AppPaddingOnly.top),
        child: ListView(
          children: [
            const SizedBox(height: 2),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Breakfast',
                      style: AppTextStyle.cardHeaderText,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Text(
                          'Calories: 500',
                          style: AppTextStyle.bodyText,
                        ),
                        const Spacer(),
                        TextButton(
                          style: ButtonStyle(
                            shape: WidgetStateProperty.all<OutlinedBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                            ),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColours.appOrgange),
                          ),
                          onPressed: () {},
                          child: const Text(
                            '+ Add Breakfast',
                            style: AppTextStyle.textButton,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Lunch',
                      style: AppTextStyle.cardHeaderText,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Text(
                          'Calories: 500',
                          style: AppTextStyle.bodyText,
                        ),
                        const Spacer(),
                        TextButton(
                          style: ButtonStyle(
                            shape: WidgetStateProperty.all<OutlinedBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                            ),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColours.appOrgange),
                          ),
                          onPressed: () {},
                          child: const Text(
                            '+ Add Lunch',
                            style: AppTextStyle.textButton,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Dinner',
                      style: AppTextStyle.cardHeaderText,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Text(
                          'Calories: 500',
                          style: AppTextStyle.bodyText,
                        ),
                        const Spacer(),
                        TextButton(
                          style: ButtonStyle(
                            shape: WidgetStateProperty.all<OutlinedBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                            ),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColours.appOrgange),
                          ),
                          onPressed: () {},
                          child: const Text(
                            '+ Add Dinner',
                            style: AppTextStyle.textButton,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Snacks',
                      style: AppTextStyle.cardHeaderText,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Text(
                          'Calories: 500',
                          style: AppTextStyle.bodyText,
                        ),
                        const Spacer(),
                        TextButton(
                          style: ButtonStyle(
                            shape: WidgetStateProperty.all<OutlinedBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                            ),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColours.appOrgange),
                          ),
                          onPressed: () {},
                          child: const Text(
                            '+ Add Snacks',
                            style: AppTextStyle.textButton,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'Health',
                style: AppTextStyle.cardHeaderText,
              ),
            ),
            const SizedBox(height: 2),
            Card(
              //color: AppColours.appBlue,
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Water',
                      style: AppTextStyle.cardHeaderText,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Text(
                          'Litres: 2',
                          style: AppTextStyle.bodyText,
                        ),
                        const Spacer(),
                        TextButton(
                          style: ButtonStyle(
                            shape: WidgetStateProperty.all<OutlinedBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                            ),
                            backgroundColor: WidgetStateProperty.all<Color>(
                              AppColours.appBlueDark,
                            ),
                          ),
                          onPressed: () {
                            context.push('/addWaterScreen');
                          },
                          child: const Text(
                            '+ Add Water',
                            style: AppTextStyle.textButton,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Workout',
                      style: AppTextStyle.cardHeaderText,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        // const Text(
                        //   'Weight: 70kg',
                        //   style: AppTextStyle.bodyText,
                        // ),
                        const Spacer(),
                        TextButton(
                          style: ButtonStyle(
                            shape: WidgetStateProperty.all<OutlinedBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                            ),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColours.appOrgange),
                          ),
                          onPressed: () {
                            di<WorkOutNotifier>().resetExercises();
                            context.push('/addWorkoutScreen');
                          },
                          child: const Text(
                            '+ Add Workouts',
                            style: AppTextStyle.textButton,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Weight',
                      style: AppTextStyle.cardHeaderText,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Text(
                          'Weight: 70kg',
                          style: AppTextStyle.bodyText,
                        ),
                        const Spacer(),
                        TextButton(
                          style: ButtonStyle(
                            shape: WidgetStateProperty.all<OutlinedBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                            ),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColours.appOrgange),
                          ),
                          onPressed: () {
                            context.push('/enterWeightScreen');
                          },
                          child: const Text(
                            '+ Add Weight',
                            style: AppTextStyle.textButton,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 80),
          ],
        ),
      ),
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     context.push('/addFoodScreen');
      //   },
      //   child: const Icon(Icons.add),
      // ),
    );
  }
}
