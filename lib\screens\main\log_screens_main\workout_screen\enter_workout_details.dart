import 'package:flutter/material.dart';
import 'package:lifeitude/constraints/app_text_style.dart';
import 'package:lifeitude/local_db/models/exercies_list.dart';
import 'package:watch_it/watch_it.dart';

import '../../../../components/time_picker_scroll.dart';
import '../../../../constraints/app_colours.dart';
import '../../../../constraints/app_padding_only.dart';
import '../../../../widgets/my_appbar.dart';
import 'notifiers/work_out_notifier.dart';

class EnterWorkoutDetails extends StatelessWidget with WatchItMixin {
  const EnterWorkoutDetails({
    super.key,
    required this.exerciseList,
  });

  final ExerciseList exerciseList;

  @override
  Widget build(BuildContext context) {
    debugPrint(exerciseList.name);
    debugPrint(exerciseList.caloriesBurned.toString());

    final workoutNotifier = watchIt<WorkOutNotifier>();
    return Scaffold(
      appBar: const MyAppBar(
        dontShowProfileIcon: true,
        title: 'Enter Workout',
        canPop: true,
        //backgroundColor: AppColours.appPurple,
      ),
      body: Padding(
        padding: const EdgeInsets.only(
            left: AppPaddingOnly.leftRight,
            right: AppPaddingOnly.leftRight,
            top: AppPaddingOnly.top),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              exerciseList.name,
              style: AppTextStyle.bodyText,
            ),
            TimePickerScroll(
              caloriesBurned: exerciseList.caloriesBurned,
              onTimeChanged: (hours, minutes, totalCalories) {
                debugPrint(
                    'Selected Time: $hours:${minutes.toString().padLeft(2, '0')}');
                debugPrint('Total Calories: $totalCalories');

                di<WorkOutNotifier>().setWorkoutTime(
                  hours: hours,
                  minutes: minutes,
                  totalCalories: totalCalories,
                );
              },
            ),
            const SizedBox(height: 10),
            const Divider(
              thickness: 1,
              color: Colors.grey,
            ),
            const SizedBox(height: 10),
            Text(
              '${workoutNotifier.totalCalories} calories burned',
              style: AppTextStyle.workoutCalText,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            TextButton(
              onPressed: () {
                di<WorkOutNotifier>().saveWorkoutTime();

                // di<AddWaterNotifier>().addWaterIntake();
              },
              style: TextButton.styleFrom(
                backgroundColor: AppColours.appOrgange,
                padding: const EdgeInsets.only(left: 10, right: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Add Workout',
                style: AppTextStyle.textButton,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
