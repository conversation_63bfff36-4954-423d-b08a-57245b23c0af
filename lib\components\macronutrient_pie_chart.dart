import 'dart:math';
import 'package:flutter/material.dart';
import 'package:lifeitude/constraints/app_text_style.dart';

class Macronutrient<PERSON>ie<PERSON>hart extends StatelessWidget {
  final double fat;
  final double carbs;
  final double protein;
  final double? height;
  final double? width;

  const MacronutrientPieChart({
    super.key,
    required this.fat,
    required this.carbs,
    required this.protein,
    this.height,
    this.width,
  });

  // Calculate total macronutrients
  double get _total => fat + carbs + protein;

  // Calculate percentages
  double get _fatPercent => _total > 0 ? (fat / _total * 100) : 0;
  double get _carbPercent => _total > 0 ? (carbs / _total * 100) : 0;
  double get _proteinPercent => _total > 0 ? (protein / _total * 100) : 0;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine if we should use wide or narrow layout
        bool isWideLayout = constraints.maxWidth > 300;

        return SizedBox(
          width: width ?? double.infinity,
          height: height ?? 250,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: isWideLayout ? _buildWideLayout() : _buildNarrowLayout(),
          ),
        );
      },
    );
  }

  Widget _buildWideLayout() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Legend (40% width)
        Expanded(
          flex: 2,
          child: _buildLegend(),
        ),
        // Pie Chart (60% width)
        Expanded(
          flex: 3,
          child: AspectRatio(
            aspectRatio: 1,
            child: _buildPieChart(),
          ),
        ),
      ],
    );
  }

  Widget _buildNarrowLayout() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Pie Chart
        Expanded(
          flex: 3,
          child: AspectRatio(
            aspectRatio: 1,
            child: _buildPieChart(),
          ),
        ),
        // Legend
        Expanded(
          flex: 1,
          child: _buildLegend(),
        ),
      ],
    );
  }

  Widget _buildPieChart() {
    return CustomPaint(
      painter: _MacronutrientPiePainter(
        fatValue: _fatPercent,
        carbValue: _carbPercent,
        proteinValue: _proteinPercent,
      ),
      child: Container(),
    );
  }

  Widget _buildLegend() {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _legendItem(Colors.red, 'Fat', _fatPercent),
          const SizedBox(height: 8),
          _legendItem(Colors.blue, 'Carbs', _carbPercent),
          const SizedBox(height: 8),
          _legendItem(Colors.green, 'Protein', _proteinPercent),
        ],
      ),
    );
  }

  Widget _legendItem(Color color, String label, double percent) {
    return Row(
      children: [
        Container(
          width: 20,
          height: 20,
          color: color,
        ),
        const SizedBox(width: 8),
        Text(
          '$label (${percent.toStringAsFixed(1)}%)',
          style: AppTextStyle.chartText,
        ),
      ],
    );
  }
}

class _MacronutrientPiePainter extends CustomPainter {
  final double fatValue;
  final double carbValue;
  final double proteinValue;

  _MacronutrientPiePainter({
    required this.fatValue,
    required this.carbValue,
    required this.proteinValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;

    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    // Starting angle
    double startAngle = -pi / 2;

    // Fat slice
    paint.color = Colors.red;
    _drawPieSlice(canvas, center, radius, startAngle, fatValue, paint);
    startAngle += _calculateSweepAngle(fatValue);

    // Carbs slice
    paint.color = Colors.blue;
    _drawPieSlice(canvas, center, radius, startAngle, carbValue, paint);
    startAngle += _calculateSweepAngle(carbValue);

    // Protein slice
    paint.color = Colors.green;
    _drawPieSlice(canvas, center, radius, startAngle, proteinValue, paint);
  }

  void _drawPieSlice(Canvas canvas, Offset center, double radius,
      double startAngle, double value, Paint paint) {
    if (value > 0) {
      final sweepAngle = _calculateSweepAngle(value);
      final path = Path()
        ..moveTo(center.dx, center.dy)
        ..arcTo(
          Rect.fromCircle(center: center, radius: radius),
          startAngle,
          sweepAngle,
          false,
        )
        ..close();

      canvas.drawPath(path, paint);
    }
  }

  double _calculateSweepAngle(double percent) {
    return (percent / 100) * 2 * pi;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
