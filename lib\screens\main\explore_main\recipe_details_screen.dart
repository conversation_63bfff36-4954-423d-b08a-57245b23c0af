import 'package:flutter/material.dart';
import 'package:lifeitude/widgets/my_appbar.dart';
import 'package:watch_it/watch_it.dart';

import '../../../constraints/app_colours.dart';
import '../../../notifier_controllers/recipes_controller.dart';

class RecipeDetailsScreen extends StatelessWidget with WatchItMixin {
  final String recipeId;

  const RecipeDetailsScreen({super.key, required this.recipeId});
  @override
  Widget build(BuildContext context) {
    final recipesController = watchIt<RecipesController>();
    final recipeDetails = recipesController.recipeDetailsModel;
    final recipe = recipeDetails?.recipe;

    return Scaffold(
      backgroundColor: AppColours.appLiteOrange,
      appBar: MyAppBar(title: 'Recipe Details', canPop: true),
      body: recipesController.isLoading
          ? const Center(child: CircularProgressIndicator())
          : recipe == null
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Recipe not found',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildRecipeHeader(recipe),
                  const SizedBox(height: 24),
                  _buildRecipeInfo(recipe),
                  const SizedBox(height: 24),
                  _buildIngredients(recipe),
                  const SizedBox(height: 24),
                  _buildDirections(recipe),
                  const SizedBox(height: 24),
                  _buildNutritionFacts(recipe),
                ],
              ),
            ),
    );
  }

  Widget _buildRecipeHeader(recipe) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recipe Images
          if (recipe.recipeImages?.recipeImage != null &&
              recipe.recipeImages!.recipeImage!.isNotEmpty)
            Container(
              height: 250,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
                image: DecorationImage(
                  image: NetworkImage(recipe.recipeImages!.recipeImage!.first),
                  fit: BoxFit.cover,
                ),
              ),
            ),

          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  recipe.recipeName ?? 'Unknown Recipe',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                if (recipe.recipeDescription != null)
                  Text(
                    recipe.recipeDescription!,
                    style: const TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                const SizedBox(height: 12),
                if (recipe.rating != null)
                  Row(
                    children: [
                      const Icon(Icons.star, color: Colors.amber, size: 20),
                      const SizedBox(width: 4),
                      Text(
                        recipe.rating!,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecipeInfo(recipe) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recipe Info',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.schedule,
                    'Prep Time',
                    '${recipe.preparationTimeMin ?? 'N/A'} min',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    Icons.timer,
                    'Cook Time',
                    '${recipe.cookingTimeMin ?? 'N/A'} min',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.people,
                    'Servings',
                    recipe.numberOfServings ?? 'N/A',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    Icons.scale,
                    'Per Portion',
                    '${recipe.gramsPerPortion ?? 'N/A'}g',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(icon, color: AppColours.appOrgange, size: 24),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildIngredients(recipe) {
    if (recipe.ingredients?.ingredient == null ||
        recipe.ingredients!.ingredient!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ingredients',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...recipe.ingredients!.ingredient!
                .map(
                  (ingredient) => Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 6,
                          height: 6,
                          margin: const EdgeInsets.only(top: 8, right: 12),
                          decoration: BoxDecoration(
                            color: AppColours.appOrgange,
                            shape: BoxShape.circle,
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                ingredient.ingredientDescription ??
                                    ingredient.foodName ??
                                    'Unknown ingredient',
                                style: const TextStyle(fontSize: 16),
                              ),
                              if (ingredient.measurementDescription != null)
                                Text(
                                  ingredient.measurementDescription!,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildDirections(recipe) {
    if (recipe.directions?.direction == null ||
        recipe.directions!.direction!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Directions',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...recipe.directions!.direction!
                .map(
                  (direction) => Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: AppColours.appOrgange,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              direction.directionNumber ?? '?',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            direction.directionDescription ?? 'No description',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionFacts(recipe) {
    final nutrition = recipe.servingSizes?.serving;
    if (nutrition == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Nutrition Facts',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (nutrition.calories != null)
              _buildNutritionItem(
                'Calories',
                nutrition.calories!,
                Icons.local_fire_department,
              ),
            if (nutrition.protein != null)
              _buildNutritionItem(
                'Protein',
                '${nutrition.protein}g',
                Icons.fitness_center,
              ),
            if (nutrition.carbohydrate != null)
              _buildNutritionItem(
                'Carbs',
                '${nutrition.carbohydrate}g',
                Icons.grain,
              ),
            if (nutrition.fat != null)
              _buildNutritionItem('Fat', '${nutrition.fat}g', Icons.opacity),
            if (nutrition.fiber != null)
              _buildNutritionItem('Fiber', '${nutrition.fiber}g', Icons.eco),
            if (nutrition.sugar != null)
              _buildNutritionItem('Sugar', '${nutrition.sugar}g', Icons.cake),
            if (nutrition.sodium != null)
              _buildNutritionItem(
                'Sodium',
                '${nutrition.sodium}mg',
                Icons.grain,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: AppColours.appOrgange, size: 20),
          const SizedBox(width: 12),
          Expanded(child: Text(label, style: const TextStyle(fontSize: 16))),
          Text(
            value,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
