import 'package:lifeitude/objectbox.g.dart';

import '../local_db/models/daily_calories_intake.dart';
import 'i_daily_calories_intake.dart';

class DailyCaloriesIntakeRepository implements IDailyCaloriesIntake {
  final Box<DailyCaloriesIntake> _box;

  DailyCaloriesIntakeRepository(this._box);

  @override
  Future<void> addDailyCaloriesIntake(int calories) async {
    final newDailyCaloriesIntake = DailyCaloriesIntake(
      id: 0, // Let ObjectBox auto-generate the ID
      date: DateTime.now(),
      calories: calories,
    );

    _box.put(newDailyCaloriesIntake);
  }

  @override
  Future<int> getDailyCaloriesIntake() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

    // Create ObjectBox query to filter by today's date
    final query = _box
        .query(
          DailyCaloriesIntake_.date.greaterOrEqual(
                todayStart.millisecondsSinceEpoch,
              ) &
              DailyCaloriesIntake_.date.lessOrEqual(
                todayEnd.millisecondsSinceEpoch,
              ),
        )
        .build();

    final results = query.find();
    query.close();

    if (results.isNotEmpty) {
      return results.first.calories;
    } else {
      return 2000;
    }
  }

  @override
  Future<void> deleteDailyCaloriesIntake(int id) async {
    _box.remove(id);
  }
}
