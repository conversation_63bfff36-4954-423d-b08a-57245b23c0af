import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';
import '../../../../constraints/app_colours.dart';
import '../../../../constraints/app_padding_only.dart';
import '../../../../notifier_controllers/recipes_controller.dart';

class RecipesScreen extends StatelessWidget with WatchItMixin {
  const RecipesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final recipes = watchIt<RecipesController>();

    return Scaffold(
      backgroundColor: AppColours.appLitePurple,
      body: recipes.isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.only(
                left: AppPaddingOnly.leftRight,
                right: AppPaddingOnly.leftRight,
                top: AppPaddingOnly.top,
              ),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.1,
                ),
                itemCount: recipes.recipesTypes.length,
                itemBuilder: (context, index) {
                  final recipeType = recipes.recipesTypes[index];
                  return InkWell(
                    onTap: () {
                      context.push(
                        '/recipeSearch?recipeType=${Uri.encodeComponent(recipeType)}',
                      );
                    },
                    borderRadius: BorderRadius.circular(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColours.appOrgange.withValues(alpha: 0.1),
                              AppColours.appOrgange.withValues(alpha: 0.05),
                            ],
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: AppColours.appOrgange.withValues(
                                    alpha: 0.2,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  _getRecipeIcon(recipeType),
                                  size: 42,
                                  color: AppColours.appGreen,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                recipeType,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
    );
  }

  IconData _getRecipeIcon(String recipeType) {
    switch (recipeType.toLowerCase()) {
      case 'appetizer':
        return Icons.restaurant_menu;
      case 'soup':
        return Icons.soup_kitchen;
      case 'main dish':
        return Icons.dinner_dining;
      case 'side dish':
        return Icons.rice_bowl;
      case 'baked':
        return Icons.bakery_dining;
      case 'salad and salad dressing':
        return Icons.eco;
      case 'sauce and condiment':
        return Icons.water_drop;
      case 'dessert':
        return Icons.cake;
      case 'snack':
        return Icons.cookie;
      case 'beverage':
        return Icons.local_drink;
      case 'breakfast':
        return Icons.free_breakfast;
      case 'lunch':
        return Icons.lunch_dining;
      case 'other':
        return Icons.more_horiz;
      default:
        return Icons.restaurant;
    }
  }
}
