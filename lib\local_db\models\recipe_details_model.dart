class RecipeDetailsModel {
  Recipe? recipe;

  RecipeDetailsModel({this.recipe});

  RecipeDetailsModel.fromJson(Map<String, dynamic> json) {
    recipe = json['recipe'] != null ? Recipe.fromJson(json['recipe']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (recipe != null) {
      data['recipe'] = recipe!.toJson();
    }
    return data;
  }
}

class Recipe {
  String? cookingTimeMin;
  Directions? directions;
  String? gramsPerPortion;
  Ingredients? ingredients;
  String? numberOfServings;
  String? preparationTimeMin;
  String? rating;
  RecipeCategories? recipeCategories;
  String? recipeDescription;
  String? recipeId;
  RecipeImages? recipeImages;
  String? recipeName;
  RecipeTypes? recipeTypes;
  String? recipeUrl;
  ServingSizes? servingSizes;

  Recipe({
    this.cookingTimeMin,
    this.directions,
    this.gramsPerPortion,
    this.ingredients,
    this.numberOfServings,
    this.preparationTimeMin,
    this.rating,
    this.recipeCategories,
    this.recipeDescription,
    this.recipeId,
    this.recipeImages,
    this.recipeName,
    this.recipeTypes,
    this.recipeUrl,
    this.servingSizes,
  });

  Recipe.fromJson(Map<String, dynamic> json) {
    cookingTimeMin = json['cooking_time_min'];
    directions = json['directions'] != null
        ? Directions.fromJson(json['directions'])
        : null;
    gramsPerPortion = json['grams_per_portion'];
    ingredients = json['ingredients'] != null
        ? Ingredients.fromJson(json['ingredients'])
        : null;
    numberOfServings = json['number_of_servings'];
    preparationTimeMin = json['preparation_time_min'];
    rating = json['rating'];
    recipeCategories = json['recipe_categories'] != null
        ? RecipeCategories.fromJson(json['recipe_categories'])
        : null;
    recipeDescription = json['recipe_description'];
    recipeId = json['recipe_id'];
    recipeImages = json['recipe_images'] != null
        ? RecipeImages.fromJson(json['recipe_images'])
        : null;
    recipeName = json['recipe_name'];
    recipeTypes = json['recipe_types'] != null
        ? RecipeTypes.fromJson(json['recipe_types'])
        : null;
    recipeUrl = json['recipe_url'];
    servingSizes = json['serving_sizes'] != null
        ? ServingSizes.fromJson(json['serving_sizes'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['cooking_time_min'] = cookingTimeMin;
    if (directions != null) {
      data['directions'] = directions!.toJson();
    }
    data['grams_per_portion'] = gramsPerPortion;
    if (ingredients != null) {
      data['ingredients'] = ingredients!.toJson();
    }
    data['number_of_servings'] = numberOfServings;
    data['preparation_time_min'] = preparationTimeMin;
    data['rating'] = rating;
    if (recipeCategories != null) {
      data['recipe_categories'] = recipeCategories!.toJson();
    }
    data['recipe_description'] = recipeDescription;
    data['recipe_id'] = recipeId;
    if (recipeImages != null) {
      data['recipe_images'] = recipeImages!.toJson();
    }
    data['recipe_name'] = recipeName;
    if (recipeTypes != null) {
      data['recipe_types'] = recipeTypes!.toJson();
    }
    data['recipe_url'] = recipeUrl;
    if (servingSizes != null) {
      data['serving_sizes'] = servingSizes!.toJson();
    }
    return data;
  }
}

class Directions {
  List<Direction>? direction;

  Directions({this.direction});

  Directions.fromJson(Map<String, dynamic> json) {
    if (json['direction'] != null) {
      direction = <Direction>[];
      json['direction'].forEach((v) {
        direction!.add(Direction.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (direction != null) {
      data['direction'] = direction!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Direction {
  String? directionDescription;
  String? directionNumber;

  Direction({this.directionDescription, this.directionNumber});

  Direction.fromJson(Map<String, dynamic> json) {
    directionDescription = json['direction_description'];
    directionNumber = json['direction_number'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['direction_description'] = directionDescription;
    data['direction_number'] = directionNumber;
    return data;
  }
}

class Ingredients {
  List<Ingredient>? ingredient;

  Ingredients({this.ingredient});

  Ingredients.fromJson(Map<String, dynamic> json) {
    if (json['ingredient'] != null) {
      ingredient = <Ingredient>[];
      json['ingredient'].forEach((v) {
        ingredient!.add(Ingredient.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (ingredient != null) {
      data['ingredient'] = ingredient!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Ingredient {
  String? foodId;
  String? foodName;
  String? ingredientDescription;
  String? ingredientUrl;
  String? measurementDescription;
  String? numberOfUnits;
  String? servingId;

  Ingredient({
    this.foodId,
    this.foodName,
    this.ingredientDescription,
    this.ingredientUrl,
    this.measurementDescription,
    this.numberOfUnits,
    this.servingId,
  });

  Ingredient.fromJson(Map<String, dynamic> json) {
    foodId = json['food_id'];
    foodName = json['food_name'];
    ingredientDescription = json['ingredient_description'];
    ingredientUrl = json['ingredient_url'];
    measurementDescription = json['measurement_description'];
    numberOfUnits = json['number_of_units'];
    servingId = json['serving_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['food_id'] = foodId;
    data['food_name'] = foodName;
    data['ingredient_description'] = ingredientDescription;
    data['ingredient_url'] = ingredientUrl;
    data['measurement_description'] = measurementDescription;
    data['number_of_units'] = numberOfUnits;
    data['serving_id'] = servingId;
    return data;
  }
}

class RecipeCategories {
  List<RecipeCategory>? recipeCategory;

  RecipeCategories({this.recipeCategory});

  RecipeCategories.fromJson(Map<String, dynamic> json) {
    if (json['recipe_category'] != null) {
      recipeCategory = <RecipeCategory>[];
      json['recipe_category'].forEach((v) {
        recipeCategory!.add(RecipeCategory.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (recipeCategory != null) {
      data['recipe_category'] = recipeCategory!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class RecipeCategory {
  String? recipeCategoryName;
  String? recipeCategoryUrl;

  RecipeCategory({this.recipeCategoryName, this.recipeCategoryUrl});

  RecipeCategory.fromJson(Map<String, dynamic> json) {
    recipeCategoryName = json['recipe_category_name'];
    recipeCategoryUrl = json['recipe_category_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recipe_category_name'] = recipeCategoryName;
    data['recipe_category_url'] = recipeCategoryUrl;
    return data;
  }
}

class RecipeImages {
  List<String>? recipeImage;

  RecipeImages({this.recipeImage});

  RecipeImages.fromJson(Map<String, dynamic> json) {
    recipeImage = json['recipe_image'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recipe_image'] = recipeImage;
    return data;
  }
}

class RecipeTypes {
  List<String>? recipeType;

  RecipeTypes({this.recipeType});

  RecipeTypes.fromJson(Map<String, dynamic> json) {
    recipeType = json['recipe_type'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recipe_type'] = recipeType;
    return data;
  }
}

class ServingSizes {
  Serving? serving;

  ServingSizes({this.serving});

  ServingSizes.fromJson(Map<String, dynamic> json) {
    serving = json['serving'] != null
        ? Serving.fromJson(json['serving'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (serving != null) {
      data['serving'] = serving!.toJson();
    }
    return data;
  }
}

class Serving {
  String? calcium;
  String? calories;
  String? carbohydrate;
  String? cholesterol;
  String? fat;
  String? fiber;
  String? iron;
  String? monounsaturatedFat;
  String? polyunsaturatedFat;
  String? potassium;
  String? protein;
  String? saturatedFat;
  String? servingSize;
  String? sodium;
  String? sugar;
  String? transFat;
  String? vitaminA;
  String? vitaminC;

  Serving({
    this.calcium,
    this.calories,
    this.carbohydrate,
    this.cholesterol,
    this.fat,
    this.fiber,
    this.iron,
    this.monounsaturatedFat,
    this.polyunsaturatedFat,
    this.potassium,
    this.protein,
    this.saturatedFat,
    this.servingSize,
    this.sodium,
    this.sugar,
    this.transFat,
    this.vitaminA,
    this.vitaminC,
  });

  Serving.fromJson(Map<String, dynamic> json) {
    calcium = json['calcium'];
    calories = json['calories'];
    carbohydrate = json['carbohydrate'];
    cholesterol = json['cholesterol'];
    fat = json['fat'];
    fiber = json['fiber'];
    iron = json['iron'];
    monounsaturatedFat = json['monounsaturated_fat'];
    polyunsaturatedFat = json['polyunsaturated_fat'];
    potassium = json['potassium'];
    protein = json['protein'];
    saturatedFat = json['saturated_fat'];
    servingSize = json['serving_size'];
    sodium = json['sodium'];
    sugar = json['sugar'];
    transFat = json['trans_fat'];
    vitaminA = json['vitamin_a'];
    vitaminC = json['vitamin_c'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['calcium'] = calcium;
    data['calories'] = calories;
    data['carbohydrate'] = carbohydrate;
    data['cholesterol'] = cholesterol;
    data['fat'] = fat;
    data['fiber'] = fiber;
    data['iron'] = iron;
    data['monounsaturated_fat'] = monounsaturatedFat;
    data['polyunsaturated_fat'] = polyunsaturatedFat;
    data['potassium'] = potassium;
    data['protein'] = protein;
    data['saturated_fat'] = saturatedFat;
    data['serving_size'] = servingSize;
    data['sodium'] = sodium;
    data['sugar'] = sugar;
    data['trans_fat'] = transFat;
    data['vitamin_a'] = vitaminA;
    data['vitamin_c'] = vitaminC;
    return data;
  }
}
