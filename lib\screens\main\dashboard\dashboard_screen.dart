import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';

import '../../../components/activity_ring_widget.dart';
import '../../../components/circle_rating_widget.dart';
import '../../../components/macronutrient_pie_chart.dart';
import '../../../components/weekly_bar_chart.dart';
import '../../../constraints/app_colours.dart';
import '../../../constraints/app_padding_only.dart';
import '../../../constraints/app_text_style.dart';
import '../../../constraints/design_stuff.dart';
import '../../../notifier_controllers/dashbaord_controller.dart';
import '../../../widgets/my_appbar.dart';

class Dashboard extends StatelessWidget with WatchItMixin {
  const Dashboard({super.key});

  @override
  Widget build(BuildContext context) {
    final dash = watchIt<DashboardController>();
    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: const MyAppBar(title: 'Dashboard', canPop: false),
      body: Padding(
        padding: const EdgeInsets.only(
          left: AppPaddingOnly.leftRight,
          right: AppPaddingOnly.leftRight,
          top: AppPaddingOnly.top,
        ),
        child: ListView(
          children: [
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 2),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Text(
                          'Calories',
                          style: AppTextStyle.cardHeaderText,
                        ),
                        IconButton(
                          onPressed: () {
                            _showDailyCalorieDialog(
                              context,
                              dash,
                              dash.dailyCalorieIntake,
                            );
                          },
                          icon: const Icon(Icons.edit, size: 30.0),
                          color: AppColours.appOrgange,
                        ),
                        Spacer(),
                        IconButton(
                          onPressed: () {
                            context.push('/reportsScreen');
                          },
                          icon: const Icon(Icons.open_in_new, size: 30.0),
                          color: AppColours.appOrgange,
                        ),
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: ActivityRingWidget(
                            height: 100,
                            currentValue: 250,
                            maxValue: dash.dailyCalorieIntake,
                          ),
                        ),
                        Spacer(),
                        WeeklyBarChart(
                          chartHeight: 110,
                          weeklyValues: [500, 1200, 800, 1800, 300, 2000, 1500],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: 2,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 2),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Macronutrient',
                          style: AppTextStyle.cardHeaderText,
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.open_in_new, size: 30.0),
                          color: AppColours.appOrgange,
                        ),
                      ],
                    ),
                    const MacronutrientPieChart(
                      fat: 30,
                      carbs: 40,
                      protein: 30,
                      height: 180,
                      width: double.infinity,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            Card(
              semanticContainer: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: DesignStuff.cardElevation,
              //margin: const EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 2),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Achievements',
                          style: AppTextStyle.cardHeaderText,
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.open_in_new, size: 30.0),
                          color: AppColours.appOrgange,
                        ),
                      ],
                    ),
                    const CircleRatingWidget(
                      isNetworkImage: false,
                      images: [
                        'assets/images/temp/achievement.png',
                        'assets/images/temp/achievement.png',
                        'assets/images/temp/achievement.png',
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDailyCalorieDialog(
    BuildContext context,
    DashboardController dash,
    int selectedCalories,
  ) {
    // final dash = di<DashboardController>();
    // int selectedCalories = dash.dailyCalorieIntake;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              title: Row(
                children: [
                  Icon(
                    Icons.local_fire_department,
                    color: AppColours.appOrgange,
                    size: 28,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Daily Calorie Target',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Set your daily calorie intake goal',
                    style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColours.appOrgange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Text(
                          '$selectedCalories',
                          style: const TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: AppColours.appOrgange,
                          ),
                        ),
                        const Text(
                          'calories',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  Slider(
                    value: selectedCalories.toDouble(),
                    min: 800,
                    max: 3000,
                    divisions:
                        44, // (3000-800)/50 = 44 divisions for 50-calorie steps
                    activeColor: AppColours.appOrgange,
                    inactiveColor: AppColours.appOrgange.withValues(alpha: 0.3),
                    onChanged: (value) {
                      setState(() {
                        selectedCalories =
                            (value / 50).round() * 50; // Round to nearest 50
                      });
                    },
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '800',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                      Text(
                        '3000',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: Colors.grey[600], fontSize: 16),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    di<DashboardController>().setDailyCalorieIntake(
                      selectedCalories,
                    );
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColours.appOrgange,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: const Text(
                    'Set Target',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
