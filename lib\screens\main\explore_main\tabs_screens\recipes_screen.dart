import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';
import '../../../../constraints/app_colours.dart';
import '../../../../constraints/app_padding_only.dart';
import '../../../../notifier_controllers/recipes_controller.dart';

class RecipesScreen extends StatelessWidget with WatchItMixin {
  const RecipesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final recipes = watchIt<RecipesController>();

    return Scaffold(
      backgroundColor: AppColours.appLiteOrange,
      body: recipes.isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.only(
                left: AppPaddingOnly.leftRight,
                right: AppPaddingOnly.leftRight,
                top: AppPaddingOnly.top,
              ),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                ),
                itemCount: recipes.recipesTypes.length,
                itemBuilder: (context, index) {
                  return Card(
                    elevation: 2,
                    child: Center(child: Text(recipes.recipesTypes[index])),
                  );
                },
              ),
            ),
    );
  }
}
