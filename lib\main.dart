import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:lifeitude/firebase_options.dart';
import 'package:watch_it/watch_it.dart';
import 'globalls.dart';
import 'local_db/init_object_box.dart';
import 'notifier_controllers/theme_mode_controller.dart';
import 'router/router.dart';
import 'themes/app_theme.dart' show AppTheme;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  await initObjectBox();

  await di<ThemeModeController>().initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget with WatchItMixin {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = watchIt<ThemeModeController>();
    return MaterialApp.router(
      builder: (context, child) {
        final MediaQueryData data = MediaQuery.of(context);
        return MediaQuery(
          data: data.copyWith(textScaler: TextScaler.noScaling),
          child: child!,
        );
      },
      scaffoldMessengerKey: snackbarKey,
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: theme.mode == "dark" ? ThemeMode.dark : ThemeMode.light,
    );
  }
}
