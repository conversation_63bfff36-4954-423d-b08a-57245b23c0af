import 'package:flutter/material.dart';

import '../../constraints/app_colours.dart';
import '../../constraints/app_padding_only.dart';
import '../../widgets/my_appbar.dart';

class ProfileSettingScreen extends StatelessWidget {
  const ProfileSettingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: MyAppBar(title: 'Profile', canPop: true),
      body: Padding(
        padding: EdgeInsets.only(
            left: AppPaddingOnly.leftRight,
            right: AppPaddingOnly.leftRight,
            top: AppPaddingOnly.top),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Text('Profile Settings'),
            ],
          ),
        ),
      ),
    );
  }
}
