import 'package:flutter/material.dart';

import '../constraints/app_text_style.dart';
import 'activity_ring_painter.dart';

class ActivityRingWidget extends StatelessWidget {
  final int currentValue;
  final int maxValue;
  final double height;

  const ActivityRingWidget({
    super.key,
    required this.currentValue,
    this.maxValue = 2500,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate progress percentage
    double progress = (currentValue / maxValue).clamp(0.0, 1.0);

    String maxDisplay = maxValue.toStringAsFixed(0);

    return SizedBox(
      height: height,
      child: CustomPaint(
        painter: ActivityRingPainter(progress: progress),
        child: Center(
          child: Text(maxDisplay, style: AppTextStyle.ringText),
          // child: Text(
          //   '${(progress * 100).toStringAsFixed(1)}%',
          //   style: const TextStyle(
          //     fontSize: 20,
          //     fontWeight: FontWeight.bold,
          //   ),
          // ),
        ),
      ),
    );
  }
}
