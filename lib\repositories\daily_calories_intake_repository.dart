import 'package:flutter/material.dart';
import 'package:lifeitude/objectbox.g.dart';

import '../local_db/models/daily_calories_intake.dart';
import 'i_daily_calories_intake.dart';

class DailyCaloriesIntakeRepository implements IDailyCaloriesIntake {
  final Box<DailyCaloriesIntake> _box;

  DailyCaloriesIntakeRepository(this._box);

  @override
  Future<void> addDailyCaloriesIntake(int calories) async {
    final newDailyCaloriesIntake = DailyCaloriesIntake(
      id: 0, // Let ObjectBox auto-generate the ID
      date: DateTime.now(),
      calories: calories,
    );

    _box.put(newDailyCaloriesIntake);
  }

  @override
  Future<int> getDailyCaloriesIntake() async {
    final todaysRecord = await getTodaysCalorieRecord();
    return todaysRecord?.calories ?? 2000;
  }

  @override
  Future<DailyCaloriesIntake?> getTodaysCalorieRecord() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

    // Create ObjectBox query to filter by today's date
    final query = _box
        .query(
          DailyCaloriesIntake_.date.greaterOrEqual(
                todayStart.millisecondsSinceEpoch,
              ) &
              DailyCaloriesIntake_.date.lessOrEqual(
                todayEnd.millisecondsSinceEpoch,
              ),
        )
        .build();

    final results = query.find();
    query.close();

    if (results.isNotEmpty) {
      return results.first;
    } else {
      return null;
    }
  }

  @override
  Future<void> deleteDailyCaloriesIntake(int id) async {
    _box.remove(id);
  }

  @override
  Future<void> setTodaysCalorieIntake(int calories) async {
    // 1) Check if we have a calorie record for today
    final existingRecord = await getTodaysCalorieRecord();

    // 2) If we have one, delete it
    if (existingRecord != null) {
      debugPrint(
        'Found existing calorie record for today (ID: ${existingRecord.id}), deleting it',
      );
      await deleteDailyCaloriesIntake(existingRecord.id);
    }

    // 3) Save new one for today
    debugPrint('Saving new calorie intake for today: $calories calories');
    await addDailyCaloriesIntake(calories);
  }
}
