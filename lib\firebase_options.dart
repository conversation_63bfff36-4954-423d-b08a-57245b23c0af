// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCJOQPkU3S2EVDwNM81yDRy_GBEYUDnMLg',
    appId: '1:264459283981:web:4a8d280a7004d962256f44',
    messagingSenderId: '264459283981',
    projectId: 'lifeitude-4c438',
    authDomain: 'lifeitude-4c438.firebaseapp.com',
    storageBucket: 'lifeitude-4c438.firebasestorage.app',
    measurementId: 'G-V05H744KCC',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAr2kUsH-DTc-CYVH4dX1hOPilDPLNsPCU',
    appId: '1:264459283981:android:e60ed0d091d81bc9256f44',
    messagingSenderId: '264459283981',
    projectId: 'lifeitude-4c438',
    storageBucket: 'lifeitude-4c438.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBeJf8lzm1ydy4TAROcDE2OAUZU9eH31dk',
    appId: '1:264459283981:ios:9252ecb28ba246af256f44',
    messagingSenderId: '264459283981',
    projectId: 'lifeitude-4c438',
    storageBucket: 'lifeitude-4c438.firebasestorage.app',
    iosBundleId: 'com.dextersoftware.lifeitude',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBeJf8lzm1ydy4TAROcDE2OAUZU9eH31dk',
    appId: '1:264459283981:ios:9252ecb28ba246af256f44',
    messagingSenderId: '264459283981',
    projectId: 'lifeitude-4c438',
    storageBucket: 'lifeitude-4c438.firebasestorage.app',
    iosBundleId: 'com.dextersoftware.lifeitude',
  );
}
