import '../local_db/models/weight_log_model.dart';
import '../objectbox.g.dart';
import 'i_weight_add_local_db.dart';

class WeightAddLocalDb implements IWeightAddLocalDb {
  final Box<WeightLogModel> _box;

  WeightAddLocalDb(this._box);

  @override
  Future<void> addWeightLog(WeightLogModel weightLog) async {
    _box.put(weightLog);
  }

  @override
  Future<List<WeightLogModel>> getWeightLogs() async {
    return _box.getAll();
  }

  @override
  Future<List<WeightLogModel>> getTodaysWeightLogs() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final query = _box
        .query(
          WeightLogModel_.date.greaterOrEqual(todayStart.millisecondsSinceEpoch) &
              WeightLogModel_.date.lessOrEqual(todayEnd.millisecondsSinceEpoch),
        )
        .build();

    final results = query.find();
    query.close();

    return results;
  }

  

  @override
  Future<void> deleteWeightLog(WeightLogModel weightLog) async {
    _box.remove(weightLog.id);
  }

  @override
  Future<void> deleteAllWeightLogs() async {
    _box.removeAll();
  }

  @override
  Future<void> deleteTodaysWeightLogs() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final query = _box
        .query(
          WeightLogModel_.date.greaterOrEqual(todayStart.millisecondsSinceEpoch) &
              WeightLogModel_.date.lessOrEqual(todayEnd.millisecondsSinceEpoch),
        )
        .build();

    final results = query.find();
    query.close();

    for (final weightLog in results) {
      _box.remove(weightLog.id);
    }
  }


}
