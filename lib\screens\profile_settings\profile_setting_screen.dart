import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../../constraints/app_colours.dart';
import '../../constraints/app_padding_only.dart';
import '../../notifier_controllers/theme_mode_controller.dart';
import '../../widgets/my_appbar.dart';

class ProfileSettingScreen extends StatelessWidget with WatchItMixin {
  const ProfileSettingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = watchIt<ThemeModeController>();

    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: MyAppBar(title: 'Profile', canPop: true),
      body: Padding(
        padding: EdgeInsets.only(
          left: AppPaddingOnly.leftRight,
          right: AppPaddingOnly.leftRight,
          top: AppPaddingOnly.top,
        ),
        child: ListView(
          children: [
            Card(
              elevation: 1,
              child: ListTile(
                title: const Text(
                  'Light/Dark Mode',
                  style: TextStyle(fontSize: 20),
                ),
                subtitle: Text(
                  'Theme : ${theme.mode}',
                  style: TextStyle(fontSize: 18),
                ),
                trailing: Icon(size: 30, Icons.dark_mode),
                onTap: () async {
                  debugPrint("Settings: Toggling theme mode");
                  await di<ThemeModeController>().toggleMode();
                  debugPrint("Settings: Theme mode toggled");
                },
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
