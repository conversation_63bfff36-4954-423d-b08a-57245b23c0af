import 'package:objectbox/objectbox.dart';

import '../local_db/models/user_model.dart';

import 'package:watch_it/watch_it.dart';

import '../local_db/models/daily_calories_intake.dart';
import '../local_db/models/water_intake.dart';
import '../notifier_controllers/dashbaord_controller.dart';
import '../notifier_controllers/weight_log_controller.dart';
import '../repositories/add_water_local_db.dart';
import '../repositories/daily_calories_intake_repository.dart';
import '../repositories/i_add_water_local_db.dart';
import '../repositories/i_daily_calories_intake.dart';
import '../screens/explore/notifiers/add_friend_notifier.dart';
import '../screens/main/log_screens_main/water_screens/notifiers/add_water_notifier.dart';

import '../screens/main/log_screens_main/workout_screen/notifiers/work_out_notifier.dart';

initializeDependencies(Store store) {
  di.registerSingleton<Store>(store);

  di.registerSingleton<Box<User>>(store.box<User>());

  di.registerLazySingleton<AddFriendNotifier>(() => AddFriendNotifier());

  di.registerLazySingleton<WorkOutNotifier>(() => WorkOutNotifier());

  di.registerLazySingleton<Box<WaterIntake>>(() => store.box<WaterIntake>());

  di.registerLazySingleton<IAddWaterLocalDb>(
    () => AddWaterLocalDb(di<Box<WaterIntake>>()),
  );

  di.registerLazySingleton<AddWaterNotifier>(() => AddWaterNotifier());

  // Daily Calories Intake Repository
  di.registerLazySingleton<Box<DailyCaloriesIntake>>(
    () => store.box<DailyCaloriesIntake>(),
  );

  di.registerLazySingleton<IDailyCaloriesIntake>(
    () => DailyCaloriesIntakeRepository(di<Box<DailyCaloriesIntake>>()),
  );

  di.registerLazySingleton<DashboardController>(() => DashboardController());

  di.registerLazySingleton<WeightLogController>(() => WeightLogController());
}
