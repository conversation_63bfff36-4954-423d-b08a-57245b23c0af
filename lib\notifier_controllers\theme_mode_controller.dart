import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_dark_mode_local_db.dart';
import '../servcies/analytics_service.dart';

class ThemeModeController extends ChangeNotifier {
  String _mode = "light";
  String get mode => _mode;

  Future<void> initialize() async {
    final isDarkMode = await di<IDarkModeLocalDb>().loadPreference();
    _mode = isDarkMode ? "dark" : "light";

    notifyListeners();
  }

  Future<void> toggleMode() async {
    di<AnalyticsService>().logThemeChange(theme: _mode);

    await di<IDarkModeLocalDb>().savePreference(_mode == "light");
    _mode = _mode == "light" ? "dark" : "light";

    notifyListeners();
  }
}
