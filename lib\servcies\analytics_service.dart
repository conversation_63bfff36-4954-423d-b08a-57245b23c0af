
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';

class AnalyticsService {
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Log a custom event
  Future<void> logEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    try {
      await _analytics.logEvent(name: name, parameters: parameters);
      //debugPrint('Analytics event logged: $name, params: $parameters');
    } catch (e) {
      debugPrint('Error logging analytics event: $e');
    }
  }

  // Log when user views a specific screen
  Future<void> logScreenView({required String screenName}) async {
    try {
      await _analytics.logScreenView(screenName: screenName);
      //debugPrint('Screen view logged: $screenName');
    } catch (e) {
      debugPrint('Error logging screen view: $e');
    }
  }

  // Log when user toggles notifications
  Future<void> logToggleNotifications({required bool enabled}) async {
    await logEvent(
      name: 'toggle_notifications',
      parameters: {'enabled': enabled},
    );
  }

  // Log when user changes theme
  Future<void> logThemeChange({required String theme}) async {
    await logEvent(name: 'theme_change', parameters: {'theme': theme});
  }
}
