import 'package:flutter/material.dart';
import 'package:lifeitude/local_db/models/water_intake.dart';
import 'package:watch_it/watch_it.dart';

import '../../../../../repositories/i_add_water_local_db.dart';

class AddWaterNotifier extends ChangeNotifier {
  AddWaterNotifier() {
    init();
  }

  Future<void> init() async {
    // Add your initialization code here
    debugPrint('AddWaterNotifier initialized');

    await getWaterIntake();
  }

  double _dailyIntake = 3.0;
  double get dailyIntake => _dailyIntake;

  double _dailyIntakePercentage = 0.0;
  double get dailyIntakePercentage => _dailyIntakePercentage;

  double _currentValue = 0.3;
  double get currentValue => _currentValue;

  final List<WaterIntake> _waterIntakeList = [];
  List<WaterIntake> get waterIntakeList => _waterIntakeList;

  void editDailyIntake(double value) {
    _dailyIntake = value;
    notifyListeners();
  }

  


  void setCurrentValue(double value) {
    _currentValue = value;

    double percentage = (_currentValue / _dailyIntake) * 100;
    debugPrint('Current intake percentage: $percentage%');
    _dailyIntakePercentage = percentage;

    notifyListeners();
  }

  Future<void> addWaterIntake() async {
    final newWaterIntake = WaterIntake(
      id: 0, // Let ObjectBox auto-generate the ID
      date: DateTime.now(),
      amount: _currentValue,
    );

    await di<IAddWaterLocalDb>().addWaterIntake(newWaterIntake);

    // Refresh the list from database to get the correct ID
    await getWaterIntake();

    notifyListeners();
  }

  Future<void> getWaterIntake() async {
    _waterIntakeList.clear();
    _waterIntakeList.addAll(await di<IAddWaterLocalDb>().getWaterIntake());

    notifyListeners();
  }

  Future<void> deleteWaterIntake(WaterIntake waterIntake) async {
    _waterIntakeList.remove(waterIntake);

    await di<IAddWaterLocalDb>().deleteWaterIntake(waterIntake);

    notifyListeners();
  }
}
