import 'package:flutter/material.dart';
import 'package:lifeitude/local_db/models/water_intake.dart';
import 'package:watch_it/watch_it.dart';

import '../../../../../repositories/i_add_water_local_db.dart';

class AddWaterNotifier extends ChangeNotifier {
  AddWaterNotifier() {
    init();
  }

  Future<void> init() async {
    debugPrint('AddWaterNotifier initialized');
    await getWaterIntake();
  }

  double _dailyIntake = 3.0;
  double get dailyIntake => _dailyIntake;

  double _dailyIntakePercentage = 0.0;
  double get dailyIntakePercentage => _dailyIntakePercentage;

  double _currentValue = 0.3;
  double get currentValue => _currentValue;

  final List<WaterIntake> _waterIntakeList = [];
  List<WaterIntake> get waterIntakeList => _waterIntakeList;

  void editDailyIntake(double value) {
    _dailyIntake = value;
    notifyListeners();
  }

  bool calculateIfOverDailyIntake() {
    double totalIntake = _waterIntakeList.fold(
      0.0,
      (sum, intake) => sum + intake.amount,
    );

    _dailyIntakePercentage = (_dailyIntake > 0)
        ? (totalIntake / _dailyIntake) * 100
        : 0.0;

    if (totalIntake >= _dailyIntake) {
      debugPrint(
        '🎉 Daily water intake goal achieved! Total: ${totalIntake.toStringAsFixed(1)}L / Target: ${_dailyIntake.toStringAsFixed(1)}L',
      );
      return true;
    } else {
      debugPrint(
        '💧 Progress: ${totalIntake.toStringAsFixed(1)}L / ${_dailyIntake.toStringAsFixed(1)}L (${_dailyIntakePercentage.toStringAsFixed(0)}%)',
      );
      return false;
    }
  }

  bool get isDailyIntakeAchieved {
    double totalIntake = _waterIntakeList.fold(
      0.0,
      (sum, intake) => sum + intake.amount,
    );
    return totalIntake >= _dailyIntake;
  }

  double get totalWaterIntake {
    return _waterIntakeList.fold(0.0, (sum, intake) => sum + intake.amount);
  }

  void setCurrentValue(double value) {
    _currentValue = value;

    // double percentage = (_currentValue / _dailyIntake) * 100;
    // debugPrint('Current intake percentage: $percentage%');
    // _dailyIntakePercentage = percentage;

    calculateIfOverDailyIntake();

    notifyListeners();
  }

  Future<void> addWaterIntake() async {
    final newWaterIntake = WaterIntake(
      id: 0, // Let ObjectBox auto-generate the ID
      date: DateTime.now(),
      amount: _currentValue,
    );

    await di<IAddWaterLocalDb>().addWaterIntake(newWaterIntake);

    await getWaterIntake();

    calculateIfOverDailyIntake();

    notifyListeners();
  }

  Future<void> getWaterIntake() async {
    _waterIntakeList.clear();
    _waterIntakeList.addAll(await di<IAddWaterLocalDb>().getWaterIntake());

    calculateIfOverDailyIntake();

    notifyListeners();
  }

  Future<void> deleteWaterIntake(WaterIntake waterIntake) async {
    _waterIntakeList.remove(waterIntake);

    await di<IAddWaterLocalDb>().deleteWaterIntake(waterIntake);

    calculateIfOverDailyIntake();

    notifyListeners();
  }
}
