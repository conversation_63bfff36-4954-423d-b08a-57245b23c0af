{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:7615383821406597893", "lastPropertyId": "4:7859629875939235061", "name": "User", "properties": [{"id": "1:4445577906741154521", "name": "id", "type": 6, "flags": 1}, {"id": "2:7160566873683919576", "name": "name", "type": 9}, {"id": "3:4266825367440328609", "name": "email", "type": 9}, {"id": "4:7859629875939235061", "name": "googleID", "type": 9}], "relations": []}], "lastEntityId": "1:7615383821406597893", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}