import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../local_db/models/weight_log_model.dart';
import '../repositories/i_weight_add_local_db.dart';

class WeightLogController extends ChangeNotifier {
  WeightLogController() {
    init();
  }

  List<WeightLogModel> _weightLogs = [];
  List<WeightLogModel> get weightLogs => _weightLogs;

  double _todaysWeight = 0;
  double get todaysWeight => _todaysWeight;

  Future<void> init() async {
    debugPrint('WeightLogController initialized');

    //await getWeightLogs();
    await getTodaysWeightLogs();
    await getTodaysWeight();
  }

  Future<void> getWeightLogs() async {
    _weightLogs = await di<IWeightAddLocalDb>().getWeightLogs();
    notifyListeners();
  }

  Future<void> setWeight(double parse) async {
    debugPrint('Weight set to: $parse');

    try {
      final newWeightLog = WeightLogModel(
        id: 0,
        weight: parse,
        date: DateTime.now(),
      );

      await di<IWeightAddLocalDb>().addWeightLog(newWeightLog);

      debugPrint('Weight log added to database');

      // Refresh today's weight to get the latest entry
      getTodaysWeight();
    } catch (e) {
      debugPrint('Error adding weight log: $e');
    }

    notifyListeners();
    await getWeightLogs();
  }

  Future<void> getTodaysWeight() async {
    final toDayweightLogs = await di<IWeightAddLocalDb>().getTodaysWeightLogs();

    if (toDayweightLogs.isNotEmpty) {
      // Sort by date to get the most recent entry (last one)
      toDayweightLogs.sort((a, b) => b.date.compareTo(a.date));

      // Get the weight from the most recent entry
      _todaysWeight = toDayweightLogs.first.weight;

      debugPrint(
        'Latest weight for today: $_todaysWeight kg at ${toDayweightLogs.first.date}',
      );
    } else {
      _todaysWeight = 0.0;
      debugPrint('No weight entries found for today');
    }

    notifyListeners();
  }

  Future<void> getTodaysWeightLogs() async {
    final toDayweightLogs = await di<IWeightAddLocalDb>().getTodaysWeightLogs();

    _weightLogs = toDayweightLogs;
    notifyListeners();
  }
}
