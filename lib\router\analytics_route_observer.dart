import 'package:flutter/material.dart';
import 'package:lifeitude/servcies/analytics_service.dart';
import 'package:watch_it/watch_it.dart';

class AnalyticsRouteObserver extends NavigatorObserver {
  final _analytics = di<AnalyticsService>();

  final Map<String, String> _routeNameMap = {
    '/': 'Main Screen',
    '/profileSettingScreen': 'Profile Setting Screen',
    '/addWaterScreen': 'Add Water Screen',
    '/addFriendScreen': 'Add Friend Screen',
    '/addWorkoutScreen': 'Add Workout Screen',
    '/scheduleVrWorkout': 'Schedule VR Workout',
    '/enterWeightScreen': 'Enter Weight Screen',
    '/reportsScreen': 'Reports Screen',
    '/recipeSearch': 'Recipe Search Screen',
    '/recipeDetails': 'Recipe Details Screen',
    '/addFoodScreen': 'Add Food Screen',
  };

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _logScreenView(route);
    super.didPush(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    if (newRoute != null) {
      _logScreenView(newRoute);
    }
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    if (previousRoute != null) {
      _logScreenView(previousRoute);
    }
    super.didPop(route, previousRoute);
  }

  void _logScreenView(Route<dynamic> route) {
    final settings = route.settings;
    final String? routePath = settings.name;

    if (routePath != null) {
      final screenName = _routeNameMap[routePath] ?? routePath;

      _analytics.logScreenView(screenName: screenName);
    } else {
      // For routes without a name, try to extract useful information
      final routeString = route.toString();

      // Try to find a matching route path in our map
      for (final path in _routeNameMap.keys) {
        if (routeString.contains(path)) {
          final screenName = _routeNameMap[path]!;

          _analytics.logScreenView(screenName: screenName);
          return;
        }
      }

      // If no match found, log a generic screen view
      _analytics.logScreenView(screenName: 'Unknown Screen');
    }
  }
}
