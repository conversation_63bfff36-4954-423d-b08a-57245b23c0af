import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../../../../constraints/app_padding_only.dart';
import '../../../../constraints/app_text_style.dart';
import '../../../../widgets/my_appbar.dart';
import 'enter_workout_details.dart';
import 'notifiers/work_out_notifier.dart';

class AddWorkoutScreen extends StatelessWidget with WatchItMixin {
  const AddWorkoutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final workoutNotifier = watchIt<WorkOutNotifier>();
    return Scaffold(
      appBar: const MyAppBar(
        dontShowProfileIcon: true,
        title: 'Add Workout',
        canPop: true,
        //backgroundColor: AppColours.appPurple,
      ),
      body: Padding(
        padding: const EdgeInsets.only(
            left: AppPaddingOnly.leftRight,
            right: AppPaddingOnly.leftRight,
            top: AppPaddingOnly.top),
        child: Column(
          children: [
            TextField(
              onChanged: workoutNotifier.searchExercises,
              decoration: const InputDecoration(
                hintText: 'Search for exercises',
                prefixIcon: Icon(Icons.search),
                // suffixIcon: IconButton(
                //   icon: const Icon(
                //     Icons.clear,
                //   ),
                //   onPressed: () {
                //     workoutNotifier.searchExercises('');
                //   },
                // ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                itemCount: workoutNotifier.filteredExercises.length,
                itemBuilder: (context, index) {
                  //final exercise = workoutNotifier.filteredExercises[index];
                  return ListTile(
                    title: Text(
                      workoutNotifier.filteredExercises[index].name,
                      style: AppTextStyle.bodyText,
                    ),
                    onTap: () {
                      di<WorkOutNotifier>().resetWorkoutTime();

                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EnterWorkoutDetails(
                              exerciseList:
                                  workoutNotifier.filteredExercises[index]),
                        ),
                      );
                    },
                    trailing: const Icon(Icons.add),
                  );
                },
                shrinkWrap: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
