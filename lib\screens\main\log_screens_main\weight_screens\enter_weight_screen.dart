import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';

import '../../../../constraints/app_colours.dart';
import '../../../../constraints/app_padding_only.dart';
import '../../../../constraints/app_text_style.dart';
import '../../../../notifier_controllers/weight_log_controller.dart';
import '../../../../widgets/my_appbar.dart';

class EnterWeightScreen extends WatchingStatefulWidget {
  const EnterWeightScreen({super.key});

  @override
  State<EnterWeightScreen> createState() => _EnterWeightScreenState();
}

class _EnterWeightScreenState extends State<EnterWeightScreen> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _weightController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _weightController.clear();
  }

  @override
  void dispose() {
    _weightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final weightLog = watchIt<WeightLogController>();
    return Scaffold(
      backgroundColor: AppColours.appBGgreen,
      appBar: const MyAppBar(
        dontShowProfileIcon: true,
        title: 'Add Weight',
        canPop: true,
        //backgroundColor: AppColours.appPurple,
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.only(
            left: AppPaddingOnly.leftRight,
            right: AppPaddingOnly.leftRight,
            top: AppPaddingOnly.top,
          ),
          child: Column(
            children: [
              const SizedBox(height: 20),
              TextFormField(
                controller: _weightController,
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Please enter your weight';
                  }

                  // Check if the value is a valid number
                  final double? weight = double.tryParse(value);
                  if (weight == null) {
                    return 'Please enter a valid number';
                  }

                  // Optional: Check for reasonable weight range
                  if (weight <= 0) {
                    return 'Weight must be greater than 0';
                  }

                  if (weight > 1000) {
                    return 'Please enter a realistic weight';
                  }

                  return null;
                },
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white,
                  suffixText: 'kg',
                  suffixStyle: TextStyle(
                    color: Colors.grey[900],
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  contentPadding: const EdgeInsets.all(12),
                  hintText: 'Your weight in kg',
                  hintStyle: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColours.appOrgange,
                  padding: const EdgeInsets.all(8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    debugPrint(_weightController.text.trim());

                    FocusScope.of(context).unfocus();

                    di<WeightLogController>().setWeight(
                      double.parse(_weightController.text.trim()),
                    );

                    context.pop();
                  }
                },
                child: Text(
                  'Add Weight',
                  style: AppTextStyle.buttonText.copyWith(color: Colors.white),
                ),
              ),
              const SizedBox(height: 20),
              ListView.builder(
                shrinkWrap: true,
                itemCount: weightLog.weightLogs.length,
                itemBuilder: (context, index) {
                  return Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                    elevation: 2,
                    child: ListTile(
                      title: Text(
                        'You logged ${weightLog.weightLogs[index].weight}kg',
                        style: AppTextStyle.bodyText,
                      ),
                      subtitle: Text(
                        'on ${weightLog.weightLogs[index].date.day}/${weightLog.weightLogs[index].date.month}/${weightLog.weightLogs[index].date.year} ${weightLog.weightLogs[index].date.hour}:${weightLog.weightLogs[index].date.minute}',
                        style: AppTextStyle.bodyText,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
