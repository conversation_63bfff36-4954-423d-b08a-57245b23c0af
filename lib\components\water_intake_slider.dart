import 'package:flutter/material.dart';

class WaterIntakeSlider extends StatelessWidget {
  final double minValue;
  final double maxValue;
  final double currentValue;
  final double width;
  final double height;
  final ValueChanged<double>? onChanged;

  const WaterIntakeSlider({
    super.key,
    this.minValue = 0.0,
    this.maxValue = 2.0,
    required this.currentValue,
    this.width = 300.0,
    this.height = 80.0,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.blue.shade100, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.shade50,
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Water fill
          Positioned(
            top: 0,
            bottom: 0,
            left: 0,
            child: Container(
              width: (width - 40) * (currentValue / maxValue),
              decoration: BoxDecoration(
                color: Colors.lightBlue.shade200,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                ),
              ),
            ),
          ),
          // Slider
          Positioned(
            left: 20,
            right: 20,
            top: 0,
            bottom: 0,
            child: Slider(
              value: currentValue,
              min: minValue,
              max: maxValue,
              activeColor: Colors.blue.shade300,
              inactiveColor: Colors.blue.shade100,
              onChanged: onChanged,
            ),
          ),
          // Value Display
          Positioned(
            right: 10,
            top: 0,
            bottom: 0,
            child: Center(
              child: Text(
                '${currentValue.toStringAsFixed(1)} L',
                style: TextStyle(
                  color: Colors.blue.shade900,
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
