import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../config/api_config.dart';
import '../config/server_config.dart';
import 'i_backend_api.dart';

class BackendApi implements IBackendApi {
  final Dio _dio = Dio();
  final String _baseUrl = ServerConfig.serverUrl;
  final String _bearerToken = ApiConfig.backendApiKey;

  BackendApi() {
    // Configure Dio to handle JSON properly
    _dio.options.responseType = ResponseType.json;
    _dio.options.sendTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  @override
  Future<Response> getResipesTypes() async {
    final response = await _dio.get(
      '$_baseUrl/recipes-types',
      options: Options(
        headers: {
          'Authorization': 'Bearer $_bearerToken',
          'Content-Type': 'application/json; charset=utf-8',
        },
        contentType: 'application/json',
      ),
    );

    debugPrint('API Response: ${response.statusCode} - ${response.data}');

    return response;
  }

  @override
  Future<Response> getRecipesByType(String recipeType) async {
    final response = await _dio.get(
      '$_baseUrl/search-recipes-types?recipe_types=$recipeType',
      options: Options(
        headers: {
          'Authorization': 'Bearer $_bearerToken',
          'Content-Type': 'application/json; charset=utf-8',
        },
        contentType: 'application/json',
      ),
    );

    debugPrint('API Response: ${response.statusCode} - ${response.data}');

    return response;
  }
}
