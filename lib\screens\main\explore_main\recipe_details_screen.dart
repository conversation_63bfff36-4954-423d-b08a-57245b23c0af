import 'package:flutter/material.dart';
import 'package:lifeitude/widgets/my_appbar.dart';
import 'package:watch_it/watch_it.dart';

import '../../../constraints/app_colours.dart';
import '../../../notifier_controllers/recipes_controller.dart';

class RecipeDetailsScreen extends StatefulWidget {
  final String recipeId;

  const RecipeDetailsScreen({super.key, required this.recipeId});

  @override
  State<RecipeDetailsScreen> createState() => _RecipeDetailsScreenState();
}

class _RecipeDetailsScreenState extends State<RecipeDetailsScreen> with WatchItMixin {
  @override
  void initState() {
    super.initState();

    // Load recipe details when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      di<RecipesController>().getRecipeDetails(widget.recipeId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final recipesController = watchIt<RecipesController>();
    final recipeDetails = recipesController.recipeDetailsModel;
    final recipe = recipeDetails?.recipe;

    return Scaffold(
      backgroundColor: AppColours.appLiteOrange,
      appBar: MyAppBar(
        title: recipe?.recipeName ?? 'Recipe Details',
        canPop: true,
      ),
      body: recipesController.isLoading
          ? const Center(child: CircularProgressIndicator())
          : recipe == null
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'Recipe not found',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildRecipeHeader(recipe),
                      const SizedBox(height: 24),
                      _buildRecipeInfo(recipe),
                      const SizedBox(height: 24),
                      _buildIngredients(recipe),
                      const SizedBox(height: 24),
                      _buildDirections(recipe),
                      const SizedBox(height: 24),
                      _buildNutritionFacts(recipe),
                    ],
                  ),
                ),
    );
  }
